//
//  LogisticsService.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/3.
//

import Foundation
import Moya

/// 物流API服务
/// 提供物流相关的所有接口操作，包括查询物流轨迹信息
enum LogisticsService {
    /// 查询物流轨迹信息
    /// - Parameter params: 包含kuaidicom、kuaidinum、phone等参数
    case queryLogistics(params: RequestParametersConvertible)
}

extension LogisticsService: GeneralAPIService {
    var path: String {
        switch self {
        case .queryLogistics:
            return "/api/v1/express/query-logistics"
        }
    }
    
    var task: Task {
        switch self {
        case let .queryLogistics(params):
            return .requestParameters(
                parameters: params.asParameters(),
                encoding: URLEncoding.default
            )
        }
    }
}

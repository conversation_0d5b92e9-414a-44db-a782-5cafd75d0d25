//
//  PaymentManager.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/28.
//

import Foundation
import Combine
import AlipaySDK
import UIKit

/// 支付结果枚举
enum PaymentResult {
    case success(tradeNo: String)
    case failure(error: String)
    case cancelled
}



/// 支付管理器
/// 统一管理支付宝、微信等第三方支付的调用
class PaymentManager: NSObject {
    
    /// 单例实例
    static let shared = PaymentManager()
    
    /// 支付结果回调
    private var paymentCompletion: ((PaymentResult) -> Void)?
    
    private override init() {
        super.init()
    }
    
    // MARK: - 支付宝支付
    
    /// 发起支付宝支付
    /// - Parameters:
    ///   - signedString: 支付宝签名字符串
    ///   - completion: 支付结果回调
    func startAlipayPayment(signedString: String, completion: @escaping (PaymentResult) -> Void) {
        self.paymentCompletion = completion

        print("开始支付宝支付，签名字符串长度: \(signedString.count)")

//        // 检查支付宝是否安装
        guard isAlipayInstalled() else {
            print("支付宝未安装")
            completion(.failure(error: "未安装支付宝客户端"))
            return
        }

        print("支付宝已安装，准备调用SDK")

        // 调用支付宝SDK进行支付
        // 注意：这里的scheme需要替换为您在Info.plist中配置的URL Scheme
        AlipaySDK.defaultService().payOrder(signedString, fromScheme: "oxyplay", callback: { [weak self] result in
            DispatchQueue.main.async {
                if let result = result {
                    self?.handleAlipayResult(result)
                } else {
                    self?.paymentCompletion?(.failure(error: "支付宝返回结果为空"))
                }
            }
        })
    }
    
    /// 处理支付宝支付结果
    /// - Parameter result: 支付宝返回的结果字典
    private func handleAlipayResult(_ result: [AnyHashable: Any]) {
        guard let paymentCompletion = paymentCompletion else { return }

        // 打印调试信息
        print("支付宝支付结果: \(result)")

        // 解析支付宝返回结果
        if let resultStatus = result["resultStatus"] as? String {
            switch resultStatus {
            case "9000": // 支付成功
                // 尝试获取交易号，优先使用trade_no，其次使用out_trade_no
                var tradeNo = "unknown_trade_no"
                if let alipayTradeNo = result["trade_no"] as? String, !alipayTradeNo.isEmpty {
                    tradeNo = alipayTradeNo
                } else if let outTradeNo = result["out_trade_no"] as? String, !outTradeNo.isEmpty {
                    tradeNo = outTradeNo
                }
                paymentCompletion(.success(tradeNo: tradeNo))

            case "6001": // 用户取消支付
                paymentCompletion(.cancelled)

            case "4000": // 订单支付失败
                let errorMsg = result["memo"] as? String ?? "订单支付失败"
                paymentCompletion(.failure(error: errorMsg))

            case "6002": // 网络连接出错
                paymentCompletion(.failure(error: "网络连接出错，请重试"))

            default: // 其他失败情况
                let errorMsg = result["memo"] as? String ?? "支付失败，错误码：\(resultStatus)"
                paymentCompletion(.failure(error: errorMsg))
            }
        } else {
            // 无法解析结果状态
            paymentCompletion(.failure(error: "支付结果解析失败"))
        }

        // 清空回调
        self.paymentCompletion = nil
    }
    
    // MARK: - 微信支付
    
    /// 发起微信支付
    /// - Parameters:
    ///   - payParams: 微信支付参数JSON字符串
    ///   - completion: 支付结果回调
    func startWechatPayment(payParams: String, completion: @escaping (PaymentResult) -> Void) {
        print("开始微信支付，参数长度: \(payParams.count)")

        // 使用WechatManager进行微信支付
        WechatManager.shared.wechatPay(payParams: payParams) { success, tradeNo, error in
            if success, let tradeNo = tradeNo {
                completion(.success(tradeNo: tradeNo))
            } else if let error = error {
                if error.localizedDescription.contains("取消") {
                    completion(.cancelled)
                } else {
                    completion(.failure(error: error.localizedDescription))
                }
            } else {
                completion(.failure(error: "微信支付失败"))
            }
        }
    }
    


    // MARK: - 支付宝回调处理

    /// 处理支付宝URL回调
    /// - Parameter url: 支付宝返回的URL
    /// - Returns: 是否处理了该URL
    func handleAlipayCallback(url: URL) -> Bool {
        // 检查是否是支付宝回调
        guard url.scheme?.lowercased() == "oxyplay" else {
            return false
        }

        // 调用支付宝SDK处理回调
        AlipaySDK.defaultService().processOrder(withPaymentResult: url) { [weak self] result in
            DispatchQueue.main.async {
                if let result = result {
                    self?.handleAlipayResult(result)
                } else {
                    self?.paymentCompletion?(.failure(error: "支付宝回调结果解析失败"))
                }
            }
        }

        return true
    }

    /// 处理微信支付URL回调
    /// - Parameter url: 微信返回的URL
    /// - Returns: 是否处理了该URL
    func handleWechatCallback(url: URL) -> Bool {
        // 检查是否是微信回调
        guard let scheme = url.scheme?.lowercased(),
              scheme.contains("wx") || scheme == "weixin" else {
            return false
        }

        // 微信SDK会自动处理回调，这里只需要返回true表示已处理
        return WXApi.handleOpen(url, delegate: nil)
    }


}

// MARK: - 扩展方法

extension PaymentManager {
    
    /// 检查是否安装了支付宝
    /// - Returns: 是否安装支付宝
    func isAlipayInstalled() -> Bool {
        // 尝试多个支付宝URL Scheme
        let alipaySchemes = ["alipay://", "alipays://", "alipayauth://"]

        for scheme in alipaySchemes {
            if let url = URL(string: scheme) {
                let canOpen = UIApplication.shared.canOpenURL(url)
                print("检查支付宝Scheme: \(scheme), 结果: \(canOpen)")
                if canOpen {
                    return true
                }
            }
        }

        print("所有支付宝Scheme检查完毕，均无法打开")
        return false
    }

    /// 检查是否安装了微信
    /// - Returns: 是否安装微信
    func isWechatInstalled() -> Bool {
        if let url = URL(string: "weixin://") {
            return UIApplication.shared.canOpenURL(url)
        }
        return false
    }

}

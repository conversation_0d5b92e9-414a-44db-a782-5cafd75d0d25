//
//  RecommendModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/6/22.
//

import Foundation
import SmartCodable
import SwiftDate

/// 帖子类型枚举
enum RecommendType: Int, Codable {
    case unknown = 0
    case official = 1  // 官方/广告
    case findPeople = 2  // 找人
    case photography = 3  // 摄影摄像
    case findHouse = 4  // 找房
    case findIdle = 5  // 找闲置
    case findCar = 6  // 找车
    case talk = 7  // 有话说
    case askAny = 8  // 随便问
    
    var displayName: String {
        switch self {
        case .official: return "官方/广告"
        case .findPeople: return "找人"
        case .photography: return "摄影摄像"
        case .findHouse: return "找房"
        case .findIdle: return "找闲置"
        case .findCar: return "找车"
        case .talk: return "有话说"
        case .askAny: return "随便问"
        case .unknown: return "未知"
        }
    }
    var displayTextColor: UIColor {
        switch self {
        case .official: return UIColor(hexString: "05C3A3", transparency: 1)! //"官方/广告"
        case .findPeople: return UIColor(hexString: "05C3A3", transparency: 1)! //"找人"
        case .photography: return UIColor(hexString: "05C3A3", transparency: 1)! //"摄影摄像"
        case .findHouse: return UIColor(hexString: "DA7527", transparency: 1)! //"找房"
        case .findIdle: return UIColor(hexString: "05C3A3", transparency: 1)! //"找闲置"
        case .findCar: return UIColor(hexString: "2AB0FF", transparency: 1)! //"找车"
        case .talk: return UIColor(hexString: "FF3C1E", transparency: 1)! //"有话说"
        case .askAny: return UIColor(hexString: "2B2C2F", transparency: 0.8)! //"随便问"
        case .unknown: return UIColor(hexString: "05C3A3", transparency: 1)! //"未知"
        }
    }
    var displayBackColor: UIColor {
        switch self {
        case .official: return UIColor(hexString: "00FFD2", transparency: 0.08)! //"官方/广告"
        case .findPeople: return UIColor(hexString: "00FFD2", transparency: 0.08)! //"找人"
        case .photography: return UIColor(hexString: "00FFD2", transparency: 0.08)! //"摄影摄像"
        case .findHouse: return UIColor(hexString: "E38E23", transparency: 0.08)! //"找房"
        case .findIdle: return UIColor(hexString: "00FFD2", transparency: 0.08)! //"找闲置"
        case .findCar: return UIColor(hexString: "2AB0FF", transparency: 0.08)! //"找车"
        case .talk: return UIColor(hexString: "FF0F00", transparency: 0.08)! //"有话说"
        case .askAny: return UIColor(hexString: "2B2C2F", transparency: 0.08)! //"随便问"
        case .unknown: return UIColor(hexString: "00FFD2", transparency: 0.08)! //"未知"
        }
    }
}

struct RecommendUserModel: SmartCodable {
    var id: String = ""
    var nickname: String = ""
    var avatar: String = ""
}
/// 推荐帖子模型 - 统一的帖子数据模型，用于首页推荐、我的发布、收藏、点赞、下架、浏览历史等场景
struct RecommendModel: SmartCodable {
    /// 帖子ID
    var id: String = ""

    /// 发布人用户ID
    var userId: String = ""

    /// 帖子标题
    var title: String = ""

    /// 图片尺寸信息（格式：宽度/高度）
    var img_size: String = ""

    /// 发布人用户信息
    var user: RecommendUserModel?

    /// 帖子描述内容
    var description: String = ""

    /// 帖子图片URL列表
    var img_urls: [String] = []

    /// 封面图URL（通常是第一张图片）
    var img: String = ""

    /// 售价/预算
    var price: String = ""

    /// 原价
    var originPrice: String?

    /// 发布地点/位置信息
    var location: String = ""

    /// 联系方式
    var contactInfo: String = ""

    /// 帖子状态（1=发布中，2=下架，3=成交，4=草稿）
    var status: String = ""

    /// 创建时间
    var created_at: String = ""

    /// 可见性设定（1=公开，2=互关好友可见，3=仅自己可见等）
    var visibility: String = ""

    /// 是否已删除标识
    var isDeleted: String = ""

    /// 浏览量
    var views: String = ""

    /// 剩余库存数量/人数
    var quantity: String = ""

    /// 运费类型（1=包邮，2=买家支付，3=无需邮寄，4=买家自提）
    var postageType: String = ""

    /// 帖子类型编号（1=官方/广告，2=找人，3=摄影摄像，4=找房，5=找闲置，6=找车，7=有话说，8=随便问）
    var type: Int = 0

    /// 当前用户是否已点赞
    var is_liked: Bool = false

    /// 当前用户是否已收藏
    var is_favorited: Bool = false

    /// 点赞数量
    var like_count: Int = 0

    /// 评论数量
    var comment_count: Int = 0

    /// 收藏数量
    var favorite_count: Int = 0

    
    // SmartCodable属性映射
    static func mappingForKey() -> [SmartKeyTransformer]? {
        return [
            CodingKeys.img <--- "cover_img",
            CodingKeys.img <--- "img",
        ]
    }
    
    /// 计算的单元格高度（用于列表显示优化）
    var cellHeight = 0.0
    // 计算属性，方便使用枚举
    var recommendType: RecommendType {
        return RecommendType(rawValue: type) ?? .unknown
    }   
       
    var imageSize:Double{
        let minValue = 9.0 / 16.0 // ≈0.5625
        let maxValue = 16.0 / 9.0 // ≈1.7778
        guard let size = convertImageSize(self.img_size) else {
            return 1
        }
        let aspectRatio = Double(size.width) / Double(size.height)
        if aspectRatio < minValue {
               return minValue
           } else if aspectRatio > maxValue {
               return maxValue
           } else {
               return aspectRatio
           }
    }
  
    func convertImageSize(_ sizeString: String) -> (width: Int, height: Int)? {
        // 1. 分割字符串
        let components = sizeString.components(separatedBy: "/")
        
        // 2. 验证分割结果
        guard components.count == 2,
              let widthStr = components.first,
              let heightStr = components.last,
              let width = Int(widthStr),   // 字符串转整数
              let height = Int(heightStr), // 字符串转整数
              height != 0                  // 避免除零错误
        else {
            return nil
        }
        
        return (width, height)
    }

}


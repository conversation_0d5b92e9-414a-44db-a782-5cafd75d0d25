import UIKit

class AppManager {
    static let shared = AppManager()
    private init() {}
    
    // 所有初始化管理器
    private let thirdPartyManager = ThirdPartyManager.shared
    
    // 应用启动时的初始化
    func setupApplication(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) {
        configScroll()
        // 初始化第三方SDK
        thirdPartyManager.setupThirdPartyServices(application, launchOptions: launchOptions)
    }
    
    // 处理URL Scheme回调
    func handleOpenURL(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey: Any]) -> Bool {
        // 处理微信、支付宝等第三方回调
        return thirdPartyManager.handleOpenURL(app, open: url, options: options)
    }
    
    // 处理Universal Link
    func handleUniversalLink(_ userActivity: NSUserActivity) -> <PERSON><PERSON> {
        return thirdPartyManager.handleUniversalLink(userActivity)
    }

    // 处理应用进入前台
    func handleApplicationWillEnterForeground() {
        thirdPartyManager.handleApplicationWillEnterForeground()
    }
    func configScroll(){
       
        UIScrollView.appearance().contentInsetAdjustmentBehavior = .never
        UITableView.appearance().contentInsetAdjustmentBehavior = .never
        // iOS 15+ 消除 UITableView/WKWebView 顶部空白
        if #available(iOS 15.0, *) {
            UITableView.appearance().sectionHeaderTopPadding = 0
        }
    }
}

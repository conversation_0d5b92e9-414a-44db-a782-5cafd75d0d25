import ATAuthSDK
import UIKit
import RongIMKit
import Kingfisher

class ThirdPartyManager: NSObject {
    static let shared = ThirdPartyManager()

   

    private let rcIMAppKey = "1146250615209934#demo"
    private let AuthSDKKey = "c5Bfp4Qmw9ChJ4FTnEVAhT1uB8LcUuDpBgHJkceKMYLPwhQGRjDOwiCz8K9W1Da6QukJXOkCgcdFsXRFvKqI8F4FnjKXA5Ddmb8E/44qz/YNPw2nRM8LE2wJ9bIzL7ibQf/aUU69aOF+dOpRDb+2JVz7kFCcU+4IBuUeIyIxb+zZw3A05FqLwF+I2YkRX7z1TsQ3sCdte7rX9Q1j1LiWdjhchap2aRJwtCMKDUl41TQVWLiJ57oq9LBmic0FD66bwJawV/fo4tc="

    private override init() {
        super.init()
    }

    // 初始化所有第三方服务
    func setupThirdPartyServices(_: UIApplication, launchOptions: [UIApplication.LaunchOptionsKey: Any]?) {
        WechatManager.shared.setupWechatSDK()
        setAuthSDKInfo()
        setRCIM()
        setupKingfisher()
    }

    //初始号阿里云短信认证
    private func setAuthSDKInfo() {
        // 初始化SDK
       
        let handler = TXCommonHandler.sharedInstance()
        handler.setAuthSDKInfo(AuthSDKKey) { resultDic in
            print("SDK初始化结果：\(resultDic)")
        }
    }
    //初始化融云
    private func setRCIM(){
        let option: RCInitOption? = nil
        RCIM.shared().initWithAppKey(rcIMAppKey, option: option)
    }

    // 初始化 Kingfisher
    private func setupKingfisher() {
        // 使用静默模式配置
        KingfisherManager.configureSilentMode()

        // 配置重试策略
        let modifier = AnyModifier { request in
            var r = request
            r.setValue("OXYPlay/1.0", forHTTPHeaderField: "User-Agent")
            return r
        }

        // 添加到默认选项
        KingfisherManager.shared.defaultOptions.append(.requestModifier(modifier))
    }

  
    // 处理URL Scheme回调
    func handleOpenURL(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey: Any]) -> Bool {
        // 优先处理支付宝回调
        if PaymentManager.shared.handleAlipayCallback(url: url) {
            return true
        }

        // 处理微信回调（包括登录、分享、支付）
        if WXApi.handleOpen(url, delegate: WechatManager.shared) {
            return true
        }

        print("未处理的URL回调: \(url)")
        return false
    }

    // 处理Universal Link
    func handleUniversalLink(_ userActivity: NSUserActivity) -> Bool {
        // 处理微信Universal Link
        return WXApi.handleOpenUniversalLink(userActivity, delegate: WechatManager.shared)
    }

    // 处理应用进入前台
    func handleApplicationWillEnterForeground() {
        // 检查支付宝支付状态
    }
}

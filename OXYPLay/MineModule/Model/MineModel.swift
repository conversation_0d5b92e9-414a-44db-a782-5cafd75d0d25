//
//  MineModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import Foundation
import SmartCodable

// MARK: - 我发布的帖子模型

/// 我发布的帖子响应模型
struct MyPostsResponseModel: SmartCodable {
    /// 草稿帖子（仅一条，如存在）
    var draft: RecommendModel?

    /// 正式发布的帖子列表
    var posts: [RecommendModel] = []

    /// 正式发布帖子的总数量（不含草稿）
    var total: Int = 0

    /// 当前页码
    var page: Int = 1

    /// 每页条数
    var limit: Int = 10
}

// MARK: - 收藏和点赞模型

/// 收藏/点赞响应模型
struct FavoriteResponseModel: SmartCodable {
    /// 帖子列表
    var list: [RecommendModel] = []

    /// 分页信息
    var pagination: PaginationModel = PaginationModel()
}

/// 分页信息模型
struct PaginationModel: SmartCodable {
    /// 当前页码
    var page: Int = 1
    
    /// 每页条数
    var limit: Int = 10
    
    /// 总记录数
    var total: Int = 0
    
    /// 总页数
    var total_page: Int = 0
}

// MARK: - 已下架帖子模型

/// 已下架帖子响应模型
struct OfflinePostsResponseModel: SmartCodable {
    /// 服务帖列表
    var posts: [RecommendModel] = []

    /// 总记录数
    var total: Int = 0

    /// 当前页码
    var page: Int = 1

    /// 每页条数
    var limit: Int = 10
}

// MARK: - 浏览历史模型

/// 浏览历史响应模型
struct BrowseHistoryResponseModel: SmartCodable {
    /// 总记录数
    var total: Int = 0

    /// 当前页码
    var page: Int = 1

    /// 每页记录数
    var limit: Int = 10

    /// 浏览记录列表
    var list: [RecommendModel] = []
}

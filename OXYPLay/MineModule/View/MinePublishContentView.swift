//
//  MineContentView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import UIKit
import Combine

protocol MinePublishContentViewDelegate: AnyObject {
    /// 点击帖子详情
    func pushToRecommendDetail(item: RecommendModel)
    
    /// 点赞按钮点击
    func likeItem(item: RecommendModel)
    
    /// 下拉刷新时触发
    func refreshData()
    
    /// 上拉加载更多时触发
    func loadMoreData()
    
    /// 删除草稿按钮点击事件
    func draftDidTapDelete(_ model: RecommendModel)
}

/// 我的内容视图（基于RecommendView优化，只包含瀑布流部分）
class MinePublishContentView: BaseView {
    
    // MARK: - Properties
    
    var listViewDidScrollCallback: ((UIScrollView) -> ())?
    weak var delegate: MinePublishContentViewDelegate?
    
    /// 页面类型
    var pageType: MinePageType = .myPosts
    
    /// 推荐数据列表
    var recommendList: [RecommendModel] = [] {
        didSet {
            collectionView.reloadData()
        }
    }
    
    /// 草稿数据（仅在我发布的页面显示）
    var draftModel: RecommendModel? {
        didSet {
            collectionView.reloadData()
        }
    }
    
    /// 是否还有更多数据
    var hasMoreData: Bool = true
    
    // MARK: - UI Components
    
    lazy var collectionView: UICollectionView = {
        let layout = WaterfallMutiSectionFlowLayout()
        layout.delegate = self
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = color_F6F8F9
        collectionView.register(MineRecommendCell.self, forCellWithReuseIdentifier: "recommend")
        collectionView.dataSource = self
        collectionView.delegate = self
        return collectionView
    }()
    
    // MARK: - Lifecycle
    
    deinit {
        listViewDidScrollCallback = nil
    }
    
    override func configUI() {
        addSubview(collectionView)
        
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - Public Methods
    
    /// 更新推荐列表数据
    func updateRecommendList(_ models: [RecommendModel]) {
        self.recommendList = models
        collectionView.reloadData()
    }
    
    /// 添加更多推荐列表数据
    func appendRecommendList(_ models: [RecommendModel]) {
        self.recommendList.append(contentsOf: models)
        collectionView.reloadData()
    }
    
    /// 清除高度缓存
    func clearHeightCache() {
        for i in 0..<recommendList.count {
            recommendList[i].cellHeight = 0
        }
    }
    
    /// 刷新数据并清除缓存
    func reloadDataAndClearCache() {
        clearHeightCache()
        collectionView.reloadData()
    }
    
    /// 设置页面类型
    func setPageType(_ type: MinePageType) {
        pageType = type
        collectionView.reloadData()
    }
}

// MARK: - UICollectionViewDataSource

extension MinePublishContentView: UICollectionViewDataSource {
    
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        var count = recommendList.count
        
        // 如果是我发布的页面且有草稿，则增加一个草稿cell
        if pageType == .myPosts && draftModel != nil {
            count += 1
        }
        
        return count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        // 如果是我发布的页面且有草稿，第一个cell显示草稿
        if pageType == .myPosts && draftModel != nil && indexPath.item == 0 {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "recommend", for: indexPath) as! MineRecommendCell
            if let model = draftModel {
                cell.configure(with: model, pageType: .draft)
            }
            cell.delegate = self
            return cell
        }
        
        // 其他情况显示推荐内容
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "recommend", for: indexPath) as! MineRecommendCell
        
        // 计算实际的推荐列表索引
        let recommendIndex = (pageType == .myPosts && draftModel != nil) ? indexPath.item - 1 : indexPath.item
        
        if recommendIndex < recommendList.count {
            let model = recommendList[recommendIndex]
            cell.configure(with: model, pageType: pageType)
            cell.delegate = self
        }
        
        return cell
    }
}

// MARK: - UICollectionViewDelegate

extension MinePublishContentView: UICollectionViewDelegate {
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        // 如果点击的是草稿，不处理
        if pageType == .myPosts && draftModel != nil && indexPath.item == 0 {
            return
        }
        // 如果点击的是下线的，不处理
        if pageType == .offline && draftModel != nil && indexPath.item == 0 {
            return
        }
        // 计算实际的推荐列表索引
        let recommendIndex = (pageType == .myPosts && draftModel != nil) ? indexPath.item - 1 : indexPath.item
        
        if recommendIndex < recommendList.count {
            let model = recommendList[recommendIndex]
            delegate?.pushToRecommendDetail(item: model)
        }
    }
    
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        listViewDidScrollCallback?(scrollView)
    }
}

// MARK: - WaterfallMutiSectionDelegate

extension MinePublishContentView: WaterfallMutiSectionDelegate {
    
    func heightForRowAtIndexPath(collectionView: UICollectionView, layout: WaterfallMutiSectionFlowLayout, indexPath: IndexPath, itemWidth: CGFloat) -> CGFloat {
        // 如果是草稿cell
        if pageType == .myPosts && draftModel != nil && indexPath.item == 0 {
            return MineRecommendCell.calculateHeight(for: draftModel!, width: itemWidth)
        }
        
        // 推荐内容cell
        let recommendIndex = (pageType == .myPosts && draftModel != nil) ? indexPath.item - 1 : indexPath.item
        
        if recommendIndex < recommendList.count {
            let model = recommendList[recommendIndex]
            
            // 先检查模型本身是否已缓存高度
            if model.cellHeight > 0 {
                return model.cellHeight
            }
            
            let height = MineRecommendCell.calculateHeight(for: model, width: itemWidth)
            recommendList[recommendIndex].cellHeight = height
            
            return height
        }
        
        return 300
    }
    
    func columnNumber(collectionView: UICollectionView, layout: WaterfallMutiSectionFlowLayout, section: Int) -> Int {
        return 2
    }

    func insetForSection(collectionView: UICollectionView, layout: WaterfallMutiSectionFlowLayout, section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
    }

    func interitemSpacing(collectionView: UICollectionView, layout: WaterfallMutiSectionFlowLayout, section: Int) -> CGFloat {
        return 9.5
    }
    
    func lineSpacing(collectionView _: UICollectionView, layout _: WaterfallMutiSectionFlowLayout, section _: Int) -> CGFloat {
        return 8.5
    }
}


// MARK: - MineRecommendCellDelegate

extension MinePublishContentView: MineRecommendCellDelegate {
    func draftDidTapDelete(_ model: RecommendModel) {
        delegate?.draftDidTapDelete(model)
    }
    
    func recommendCellDidTapLike(_ model: RecommendModel) {
        if pageType == .likes{
            delegate?.likeItem(item: model)
        }
    }
}

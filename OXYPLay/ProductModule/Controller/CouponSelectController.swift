//
//  CouponSelectController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/18.
//

import UIKit
import SnapKit
import Combine

class CouponSelectController: BasePresentController {

    // MARK: - 属性
    var couponModel: CouponModel?

    // 优惠券选择回调 - 直接触发事件，不传递模型
    var couponSelectedPublisher = PassthroughSubject<Void, Never>()

    // 自定义分段控制器容器
    private lazy var segmentContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    // 可用优惠券按钮
    private lazy var availableButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("可用优惠券", for: .normal)
        button.setTitleColor(UIColor(hexString: "2A72FF"), for: .selected)
        button.setTitleColor(UIColor(hexString: "2B2C2F"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        button.isSelected = true
        button.tag = 0
        button.addTarget(self, action: #selector(segmentButtonTapped(_:)), for: .touchUpInside)
        return button
    }()

    // 不可用优惠券按钮
    private lazy var unavailableButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("不可用优惠券", for: .normal)
        button.setTitleColor(UIColor(hexString: "2A72FF"), for: .selected)
        button.setTitleColor(UIColor(hexString: "2B2C2F"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        button.tag = 1
        button.addTarget(self, action: #selector(segmentButtonTapped(_:)), for: .touchUpInside)
        return button
    }()


    // 滑动指示器
    private lazy var indicatorLine: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hexString: "2A72FF")
        view.layer.cornerRadius = 1
        return view
    }()

    // 表格视图
    private lazy var tableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = color_F6F8F9
        table.separatorStyle = .none
        table.showsVerticalScrollIndicator = false
        table.register(CouponTableViewCell.self, forCellReuseIdentifier: "CouponTableViewCell")
        return table
    }()

    // 当前显示的数据源
    private var currentCoupons: [CouponItemModel] {
        if availableButton.isSelected {
            return couponModel?.available ?? []
        } else {
            return couponModel?.unavailable ?? []
        }
    }

    // 实现CustomPresentable协议
    override var presentationHeight: CGFloat? {
        return kScreenHeight - 248
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
    }

    override func configUI(){
        configView(title: "优惠券", bottomTitle: "确认")

        // 添加自定义分段控制器容器
        contentView.addSubview(segmentContainerView)
        segmentContainerView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
            make.height.equalTo(30)
        }

        // 添加按钮到容器
        segmentContainerView.addSubview(availableButton)
        segmentContainerView.addSubview(unavailableButton)
        segmentContainerView.addSubview(indicatorLine)

        // 设置按钮约束
        availableButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.top.equalToSuperview()
            make.right.equalTo(segmentContainerView.snp.centerX)
        }

        unavailableButton.snp.makeConstraints { make in
            make.right.equalTo(-16)
            make.left.equalTo(segmentContainerView.snp.centerX)
            make.top.equalToSuperview()
        }


        // 设置指示器约束（初始位置在第一个按钮下方）
        indicatorLine.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
            make.height.equalTo(2)
            make.centerX.equalTo(availableButton)
            make.width.equalTo(20)
        }

        let spaceview = UIView()
        spaceview.backgroundColor = color_F6F8F9
        view.addSubview(spaceview)
        spaceview.snp.makeConstraints { make in
            make.left.right.equalTo(0)
            make.top.equalTo(segmentContainerView.snp.bottom).offset(0)
            make.height.equalTo(8)
        }
        // 添加表格视图
        contentView.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.top.equalTo(spaceview.snp.bottom).offset(0)
            make.left.right.bottom.equalToSuperview()
        }

        // 更新分段控制器标题显示数量
        updateSegmentTitles()
    }

   override func setupBindings() {
       super.setupBindings()
        // 监听底部确认按钮点击
        bottomButton.tapPublisher
            .sink { [weak self] _ in
                self?.confirmSelection()
            }
            .store(in: &cancellables)
    }

    // 更新分段控制器标题
    private func updateSegmentTitles() {
        let availableCount = couponModel?.available.count ?? 0
        let unavailableCount = couponModel?.unavailable.count ?? 0

        availableButton.setTitle("可用优惠券 \(availableCount)", for: .normal)
        unavailableButton.setTitle("不可用优惠券 \(unavailableCount)", for: .normal)
    }

    // 分段按钮点击
    @objc private func segmentButtonTapped(_ sender: UIButton) {
        availableButton.isSelected = false
        unavailableButton.isSelected = false
        sender.isSelected = true
        animateIndicator(to: sender)
        tableView.reloadData()
    }

    // 动画移动指示器
    private func animateIndicator(to button: UIButton) {
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseInOut) {
            self.indicatorLine.snp.remakeConstraints { make in
                make.bottom.equalToSuperview()
                make.height.equalTo(2)
                make.centerX.equalTo(button)
                make.width.equalTo(20)
            }
            self.segmentContainerView.layoutIfNeeded()
        }
    }

    // 确认选择
    private func confirmSelection() {
        // 直接触发选择事件，不传递具体模型
        couponSelectedPublisher.send(())
        dismiss(animated: true)
    }

    // 设置优惠券数据
    func setCouponModel(_ model: CouponModel) {
        self.couponModel = model
        updateSegmentTitles()
        tableView.reloadData()
    }

    // 获取更新后的优惠券模型
    func getUpdatedCouponModel() -> CouponModel? {
        return couponModel
    }
}

// MARK: - UITableViewDataSource
extension CouponSelectController: UITableViewDataSource {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return currentCoupons.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "CouponTableViewCell", for: indexPath) as! CouponTableViewCell

        let coupon = currentCoupons[indexPath.row]
        cell.couponItem = coupon
        cell.isAvailable = availableButton.isSelected

        // 根据模型的isSelect状态设置选中状态（仅在可用优惠券页面显示）
        if availableButton.isSelected  {
            cell.setSelected(coupon.isSelect)
        }

        return cell
    }
}

// MARK: - UITableViewDelegate
extension CouponSelectController: UITableViewDelegate {

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        // 只有可用优惠券才能选择
        if availableButton.isSelected  {
            guard var couponModel = couponModel else { return }

            let selectedCoupon = currentCoupons[indexPath.row]

            // 先清除所有可用优惠券的选中状态
            for i in 0..<couponModel.available.count {
                couponModel.available[i].isSelect = false
            }

            // 如果点击的不是已选中的优惠券，则设置为选中
            if !selectedCoupon.isSelect {
                couponModel.available[indexPath.row].isSelect = true
            }
            // 如果点击的是已选中的优惠券，上面已经清除了选中状态，相当于取消选择

            // 更新模型
            self.couponModel = couponModel

            // 刷新表格以更新选中状态
            tableView.reloadData()
        }
    }
}



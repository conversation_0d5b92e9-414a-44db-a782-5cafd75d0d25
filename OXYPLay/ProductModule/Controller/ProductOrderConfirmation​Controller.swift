//
//  ProductOrderConfirmation​Controller.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/15.
//
// MARK: - 支付方式标识符常量
struct PaymentMethodIdentifiers {
    /// 余额支付 - 对应接口的wallet
    static let wallet = "wallet"
    /// 支付宝支付 - 对应接口的alipay
    static let alipay = "alipay"
    /// 微信支付 - 对应接口的wechat
    static let wechat = "wechat"
    /// 混合支付 - 对应接口的mix
    static let mix = "mix"
}
class ProductOrderConfirmation​Controller: BaseViewController {
    // MARK: - 属性

    var detailModel: HomeDetailModel?
    var productDetailModel: ProductDetailModel?
    var dic = Dictionary<String, Any>()
    var viewModel: ProductOrderConfirmation​ViewModel?
    private lazy var toolBar = BaseTabToolBar().then {
        $0.delegate = self
    }
    lazy var listView = BaseListView().then{
        $0.delegate = self
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        configureOrderListItems()
        setupBindings()
        getData()
    }

    /// 配置UI界面
    override func configUI() {
        title = "确认订单"

        // 配置 toolBar - 初始化时显示基础文本，后续会根据金额更新
        toolBar.configureLeftMultipleRightFull(leftItems: [], rightButtonTitle: "立即支付")
        toolBar.setRightButtonGradient(colors: [UIColor(hexString: "FF5D48")!,UIColor(hexString: "FF3434")!], direction: .leftToRight)

        view.addSubview(toolBar)
        toolBar.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalTabBarHeight)
        }
        view.addSubview(listView)
        listView.snp.makeConstraints { make in
            make.top.equalTo(self.view.safeAreaLayoutGuide.snp.top).offset(12)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(toolBar.snp.top).offset(0)
        }
    }

    /// 获取数据
    func getData() {
        viewModel?.fetchLocationList()
        // 移除获取钱包余额的请求，将在支付页面获取
        if let detailModel = detailModel {
            viewModel?.fetchCouponList(advertiser_id: detailModel.user_id, order_amount: detailModel.price)
        }
        if let proDuctDetailModel = productDetailModel {
            viewModel?.fetchCouponList(advertiser_id: proDuctDetailModel.product.seller_id, order_amount: proDuctDetailModel.getcurrentQuantityPrice())
        }
    }

    /// 设置数据绑定
    override func setupBindings() {
        self.viewModel = ProductOrderConfirmation​ViewModel()

        // 监听优惠券数据变化
        viewModel?.$couponModel
            .receive(on: DispatchQueue.main)
            .sink { [weak self] model in
                guard let model = model else {return }
                self?.productDetailModel?.coupon = model
                self?.detailModel?.coupon = model
                self?.configureOrderListItems()
            }
            .store(in: &cancellables)

        // 监听地址数据变化
        viewModel?.$productAddressModel
            .receive(on: DispatchQueue.main)
            .sink { [weak self] model in
                guard let model = model else { return }
                self?.updateAddressModelInAllData(model)
                self?.refreshUI()
            }
            .store(in: &cancellables)



        // 移除钱包数据监听，因为不再在确认订单页面获取余额

        // 监听优惠券变化，用于更新底部按钮显示
        viewModel?.$couponModel
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                guard let self = self else { return }
                // 优惠券变化时更新底部按钮显示金额
                self.updateBottomButtonTitle()
            }
            .store(in: &cancellables)
        
    }
}

// MARK: - List Configuration
extension ProductOrderConfirmation​Controller {

    /// 配置订单确认列表项
    /// 统一处理商品订单和服务订单的列表配置
    /// 注意：确认订单页面不再显示支付方式选择，只显示订单信息和地址信息
    func configureOrderListItems() {
        // 获取数据模型和地址模型
        let (orderData, addressModel) = getOrderDataAndAddress()

        guard let orderData = orderData, let addressModel = addressModel else { return }

        // 创建地址项配置
        let locationItem = createLocationItem(from: addressModel)

        // 构建列表项 - 移除支付方式相关配置
        let items: [[ListItemConfig]] = [
            [
                ListItemConfig(type: .orderConfirmation, identifier: "orderConfirmation", data: orderData)
            ],
            [
                locationItem
            ]
        ]

        // 设置列表项
        listView.setItems(items)
    }

    /// 获取订单数据和地址模型
    /// - Returns: 元组包含订单数据和地址模型
    private func getOrderDataAndAddress() -> (orderData: Any?, addressModel: ProductAddressModel?) {
        if let productModel = productDetailModel {
            return (productModel, productModel.productAddressModel)
        } else if let detailModel = detailModel {
            return (detailModel, detailModel.productAddressModel)
        }
        return (nil, nil)
    }

    /// 更新底部按钮标题
    /// 显示"立即支付￥XX"，金额为到手价（原价-优惠券）
    private func updateBottomButtonTitle() {
        let totalAmount = calculateTotalAmount()
        let buttonTitle = "立即支付￥\(String(format: "%.2f", totalAmount))"
        toolBar.updateRightButtonTitle(buttonTitle)
    }

    /// 刷新UI
    private func refreshUI() {
        configureOrderListItems()
        updateBottomButtonTitle()
    }
}
// MARK: - Address Management
extension ProductOrderConfirmation​Controller {

    /// 创建地址项配置
    func createLocationItem(from addressModel: ProductAddressModel) -> ListItemConfig {
        // 检查是否有选中的地址
        if let selectedAddress = addressModel.selectedAddress() {
            // 有选中的地址，显示地址信息
            return ListItemConfig(type: .orderLocation, identifier: "orderLocation", data: addressModel)
        } else {
            // 没有选中的地址，显示"请填写收货地址"
            return ListItemConfig(type: .select, identifier: "baselist_location", placeholder: "请填写收货地址", iconString: "baselist_location")
        }
    }

    /// 显示新增地址控制器
    func showAddNewAddressController() {
        let editLocationController = EditLocationController()
        // 设置为新增模式，传入空数据
        editLocationController.dic = [:]
        editLocationController.isEditMode = false

        // 监听地区选择事件
        editLocationController.pushtoSelectLocationPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] addressData in
                self?.handleLocationSelection(addressData: addressData, editController: editLocationController)
            }
            .store(in: &cancellables)

        // 监听地址保存事件
        editLocationController.addressSavePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] addressData in
                self?.createNewAddress(addressData: addressData)
            }
            .store(in: &cancellables)

        customPresent(editLocationController, animated: true)
    }

    /// 处理地区选择
    private func handleLocationSelection(addressData: [String: Any], editController: EditLocationController) {
        // 保存当前的地址数据
        dic = addressData

        // 关闭编辑地址页面，跳转到地区选择页面
        editController.dismiss(animated: true) { [weak self] in
            self?.pushToSelectLocationForNewAddress()
        }
    }

    /// 跳转到地区选择页面（新增地址流程）
    private func pushToSelectLocationForNewAddress() {
        let locationController = ProductSelectLocationController()

        // 监听地区选择完成事件
        locationController.locationSelectedPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] (province, city, district, street) in
                self?.handleLocationSelectedForNewAddress(province: province, city: city, district: district, street: street)
            }
            .store(in: &cancellables)

        pushVcHiddenTabBar(locationController, animated: true)
    }

    /// 处理地区选择完成（新增地址流程）
    private func handleLocationSelectedForNewAddress(province: RegionListItemModel, city: RegionListItemModel?, district: RegionListItemModel?, street: RegionListItemModel?) {
        // 构建地区字符串
        var regionString = province.name
        if let city = city, city.name != province.name {
            regionString += city.name
        }
        if let district = district {
            regionString += district.name
        }
        if let street = street {
            regionString += street.name
        }

        // 更新地区信息
        dic["所在地区"] = regionString
        dic["province"] = province.name
        dic["province_code"] = province.code

        if let city = city {
            dic["city"] = city.name
            dic["city_code"] = city.code
        }

        if let district = district {
            dic["district"] = district.name
            dic["district_code"] = district.code
        }

        if let street = street {
            dic["street"] = street.name
            dic["street_code"] = street.code
        }

        // 返回到订单确认页面，然后重新弹出编辑地址页面
        navigationController?.popViewController(animated: true)

        // 延迟一点时间再弹出EditLocationController，确保页面切换完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
            self?.showEditLocationControllerWithLocationData()
        }
    }

    /// 显示带有地区数据的编辑地址控制器
    private func showEditLocationControllerWithLocationData() {
        let editLocationController = EditLocationController()
        editLocationController.dic = dic
        editLocationController.isEditMode = false

        // 监听地区选择事件（如果用户想重新选择地区）
        editLocationController.pushtoSelectLocationPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] addressData in
                self?.handleLocationSelection(addressData: addressData, editController: editLocationController)
            }
            .store(in: &cancellables)

        // 监听地址保存事件
        editLocationController.addressSavePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] addressData in
                self?.createNewAddress(addressData: addressData)
            }
            .store(in: &cancellables)

        customPresent(editLocationController, animated: true)
    }

    /// 创建新地址
    private func createNewAddress(addressData: [String: Any]) {
        // 验证必传参数
        guard let recipientName = addressData["recipient_name"] as? String, !recipientName.isEmpty,
              let phone = addressData["phone"] as? String, !phone.isEmpty,
              let province = addressData["province"] as? String, !province.isEmpty,
              let provinceCode = addressData["province_code"] as? String, !provinceCode.isEmpty,
              let city = addressData["city"] as? String, !city.isEmpty,
              let cityCode = addressData["city_code"] as? String, !cityCode.isEmpty,
              let district = addressData["district"] as? String, !district.isEmpty,
              let districtCode = addressData["district_code"] as? String, !districtCode.isEmpty,
              let street = addressData["street"] as? String, !street.isEmpty,
              let streetCode = addressData["street_code"] as? String, !streetCode.isEmpty else {
            print("创建地址失败：缺少必传参数")
            return
        }

        // 构建创建地址的参数
        var params = addressData
        params["type"] = 1 // 收货地址（必传）

        viewModel?.createAddress(addressData: params) { [weak self] success in
            DispatchQueue.main.async {
                if success {
                    print("地址创建成功")
                    // 创建成功后重新获取地址列表并刷新UI
                    self?.refreshAddressList()
                } else {
                    print("地址创建失败")
                }
            }
        }
    }

    /// 刷新地址列表
    private func refreshAddressList() {
        // 重新获取地址列表
        viewModel?.fetchLocationList()
        // 注意：UI刷新会通过setupBindings中的监听自动触发
    }

    /// 处理地址选择
    func handleAddressSelected(_ selectedAddress: ProductAddressItemModel) {
        print("订单确认页面收到选中的地址: \(selectedAddress.recipient_name)")

        // 更新ViewModel中的地址数据
        updateAddressSelection(selectedAddress)
    }

    /// 更新地址选择状态
    private func updateAddressSelection(_ selectedAddress: ProductAddressItemModel) {
        guard var addressModel = viewModel?.productAddressModel else { return }

        // 清除所有地址的选中状态
        addressModel.default_address.isSelect = false
        for i in 0..<addressModel.address_list.count {
            addressModel.address_list[i].isSelect = false
        }

        // 设置选中的地址
        if selectedAddress.id == addressModel.default_address.id {
            // 选中的是原地址
            addressModel.default_address.isSelect = true
        } else {
            // 选中的是地址列表中的某个地址
            if let index = addressModel.address_list.firstIndex(where: { $0.id == selectedAddress.id }) {
                addressModel.address_list[index].isSelect = true
            }
        }

        // 更新ViewModel中的数据，这会触发UI自动刷新
        viewModel?.productAddressModel = addressModel
    }

    /// 更新所有数据模型中的地址信息
    func updateAddressModelInAllData(_ addressModel: ProductAddressModel) {
        // 更新productDetailModel中的地址数据
        productDetailModel?.productAddressModel = addressModel

        // 更新detailModel中的地址数据
        detailModel?.productAddressModel = addressModel
    }

    /// 跳转到地区选择页面
    func pushToSelectLocation() {
        let vc = ProductSelectLocationController()
        pushVcHiddenTabBar(vc, animated: true)
        vc.locationSelectedPublisher
            .receive(on: RunLoop.main)
            .sink { [weak self] (a,b,c,d) in
                guard let self = self else { return }
                let editLocationController = EditLocationController()
                editLocationController.dic = self.dic
                editLocationController.dic["所在地区"] = a.name + (b?.name ?? "") + (c?.name ?? "") + (d?.name ?? "")
                customPresent(editLocationController, animated: true)
            }
            .store(in: &cancellables)
    }
}
// MARK: - Payment Management
extension ProductOrderConfirmation​Controller {

    /// 显示支付方式选择弹窗
    /// 点击立即支付按钮时调用，弹出CombinedPaymentController进行支付方式选择
    func showPaymentMethodSelection() {
        let combinedPaymentController = CombinedPaymentController()

        // 设置订单总金额（到手价）
        let totalAmount = calculateTotalAmount()
        combinedPaymentController.setTotalAmount(totalAmount)

        // 监听订单创建请求
        combinedPaymentController.orderCreationPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] paymentData in
                self?.handleOrderCreationRequest(paymentData: paymentData, paymentController: combinedPaymentController)
            }
            .store(in: &cancellables)
        customPresent(combinedPaymentController, animated: true)
    }

    /// 处理订单创建请求
    /// - Parameters:
    ///   - paymentData: 支付数据
    ///   - paymentController: 支付控制器
    private func handleOrderCreationRequest(paymentData: PaymentData, paymentController: CombinedPaymentController) {
        guard let viewModel = viewModel else {
            paymentController.orderCreationResultSubject.send((false, 0, "系统错误，请重试"))
            return
        }

        // 验证地址选择
        guard viewModel.productAddressModel?.selectedAddress() != nil else {
            paymentController.orderCreationResultSubject.send((false, 0, "请选择收货地址"))
            return
        }

        // 根据支付数据设置支付参数到ViewModel
        if paymentData.walletAmount > 0 && paymentData.thirdPartyAmount > 0 {
            // 混合支付
            viewModel.updatePaymentAmounts(walletAmount: paymentData.walletAmount, thirdPartyAmount: paymentData.thirdPartyAmount)
            viewModel.updateCombineThirdPartyPaymentMethod(paymentData.paymentType)
        } else if paymentData.walletAmount > 0 {
            // 纯余额支付
            viewModel.updatePaymentAmounts(walletAmount: paymentData.walletAmount, thirdPartyAmount: 0.0)
        } else {
            // 纯第三方支付
            viewModel.updatePaymentAmounts(walletAmount: 0.0, thirdPartyAmount: paymentData.thirdPartyAmount)
            viewModel.updateCombineThirdPartyPaymentMethod(paymentData.paymentType)
        }

        // 开始创建订单
        if let serviceModel = detailModel {
            createServiceOrder(serviceModel: serviceModel, paymentController: paymentController)
        } else if let productModel = productDetailModel {
            createProductOrder(productModel: productModel, paymentController: paymentController)
        } else {
            paymentController.orderCreationResultSubject.send((false, 0, "订单信息错误"))
        }
    }

    /// 创建服务订单
    private func createServiceOrder(serviceModel: HomeDetailModel, paymentController: CombinedPaymentController) {
        viewModel?.createServiceOrder(serviceModel: serviceModel) { [weak self] success, message in
            DispatchQueue.main.async {
                if success {
                    // 获取订单ID
                    if let orderId = self?.getOrderIdFromCreateResult() {
                        paymentController.orderCreationResultSubject.send((true, orderId, "订单创建成功"))
                    } else {
                        paymentController.orderCreationResultSubject.send((false, 0, "订单创建失败：无效的订单ID"))
                    }
                } else {
                    paymentController.orderCreationResultSubject.send((false, 0, message))
                }
            }
        }
    }

    /// 创建商品订单
    private func createProductOrder(productModel: ProductDetailModel, paymentController: CombinedPaymentController) {
        viewModel?.buyNowProduct(productModel: productModel) { [weak self] success, message in
            DispatchQueue.main.async {
                if success {
                    // 获取订单ID
                    if let orderId = self?.getOrderIdFromCreateResult() {
                        paymentController.orderCreationResultSubject.send((true, orderId, "订单创建成功"))
                    } else {
                        paymentController.orderCreationResultSubject.send((false, 0, "订单创建失败：无效的订单ID"))
                    }
                } else {
                    paymentController.orderCreationResultSubject.send((false, 0, message))
                }
            }
        }
    }

    /// 从创建订单结果中获取订单ID
    private func getOrderIdFromCreateResult() -> Int? {
        guard let result = viewModel?.createOrderResult else { return nil }

        if !result.order_ids.isEmpty {
            return result.order_ids.first
        } else if result.order_id > 0 {
            return result.order_id
        }

        return nil
    }





    /// 计算订单总金额
    func calculateTotalAmount() -> Float {
        if let serviceModel = detailModel {
            let servicePrice = Float(serviceModel.price) ?? 0.0
            let discountAmount = Float(viewModel?.couponModel?.getCurrentSelectedPrice() ?? "0") ?? 0.0
            return servicePrice - discountAmount
        } else if let productModel = productDetailModel {
            let productPrice = Float(productModel.getcurrentQuantityPrice()) ?? 0.0
            let discountAmount = Float(viewModel?.couponModel?.getCurrentSelectedPrice() ?? "0") ?? 0.0
            return productPrice - discountAmount
        }
        return 0.0
    }


}

// MARK: - Coupon Management
extension ProductOrderConfirmation​Controller {

    /// 处理优惠券选择完成
    func handleCouponSelectionCompleted() {
        // 优惠券选择完成，可以从viewModel的couponModel中获取当前选中的优惠券
        if let selectedCoupon = viewModel?.couponModel?.getCurrentSelectedCoupon() {
            print("选中的优惠券: \(selectedCoupon.title)")
            print("优惠券金额: ¥\(selectedCoupon.amount)")
        } else {
            print("没有选中任何优惠券")
        }

        // 更新UI显示选中的优惠券信息
        configureOrderListItems()
    }
}








extension ProductOrderConfirmation​Controller: TabToolBarDelegate {
    func tabToolBar(_ toolBar: BaseTabToolBar, didClickRightButtonAt index: Int, item: ToolBarButtonItem?) {
        // 处理立即支付按钮点击
        handlePaymentButtonClick()
    }

    /// 处理支付按钮点击
    /// 确认订单页面的立即支付按钮点击，验证信息后弹出支付方式选择页面
    private func handlePaymentButtonClick() {
        // 验证必要信息
        guard let viewModel = viewModel else {
            showErrorAlert(message: "系统错误，请重试")
            return
        }

        // 验证地址选择
        guard viewModel.productAddressModel?.selectedAddress() != nil else {
            showErrorAlert(message: "请选择收货地址")
            return
        }

        // 弹出支付方式选择页面
        showPaymentMethodSelection()
    }

    /// 显示错误提示
    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }


}

extension ProductOrderConfirmation​Controller:BaseListViewDelegate{

    func listViewUpdate(_ listView: BaseListView, with data: Any?) {
        // 确认订单页面不再处理支付方式选择更新
        // 支付方式选择已移至CombinedPaymentController中处理
    }

    func listViewClick(_ listView: BaseListView, config: ListItemConfig) {
        //没有地址时，新增地址，跳出弹窗
        if config.identifier == "baselist_location"{
            showAddNewAddressController()
        }
        //跳转地址列表
        if config.identifier == "orderLocation"{
            let vc = SelectAdressController()

            // 传递当前的地址数据给SelectAddressController
            if let currentAddressModel = viewModel?.productAddressModel {
                vc.setAddressModel(currentAddressModel)
            }

            // 监听地址选择事件
            vc.addressSelectedPublisher
                .receive(on: DispatchQueue.main)
                .sink { [weak self] selectedAddress in
                    self?.handleAddressSelected(selectedAddress)
                }
                .store(in: &cancellables)

            // 监听地址数据更新事件
            vc.addressDataUpdatedPublisher
                .receive(on: DispatchQueue.main)
                .sink { [weak self] _ in
                    // 地址数据已更新，重新获取最新数据
                    self?.viewModel?.fetchLocationList()
                }
                .store(in: &cancellables)

            pushVc(vc, animated: true)
        }
        //选择优惠卷
        if config.identifier == "orderConfirmation"{
            let vc = CouponSelectController()
            // 传递优惠券数据
            if let couponModel = viewModel?.couponModel {
                vc.setCouponModel(couponModel)
            }

            // 监听优惠券选择结果
            vc.couponSelectedPublisher
                .receive(on: RunLoop.main)
                .sink { [weak self] _ in
                    guard let self = self else { return }
                    // 获取更新后的优惠券模型并更新到viewModel
                    if let updatedCouponModel = vc.getUpdatedCouponModel() {
                        self.viewModel?.couponModel = updatedCouponModel
                        // 同时更新productDetailModel中的coupon数据
                        self.productDetailModel?.coupon = updatedCouponModel
                    }
                    // 优惠券选择完成，更新UI
                    self.handleCouponSelectionCompleted()
                }
                .store(in: &cancellables)

            customPresent(vc, animated: true)
        }
    }
}

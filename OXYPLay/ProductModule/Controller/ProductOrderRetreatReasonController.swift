//
//  ProductOrderRetreatReasonController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/2.
//

class ProductOrderRetreatReasonController: BasePresentController {

    // MARK: - Properties
    let selectCompletePublisher = PassthroughSubject<[String: Any], Never>()
    override var presentationHeight: CGFloat? {
        return nil
    }

    /// 当前选中的退款类型
    private var currentRetreatType: RetreatType = .refundAndReturn

    // MARK: - UI组件

    /// 退款类型选择视图
    lazy var retreatTypeSelectionView = RetreatTypeSelectionView().then {
        $0.delegate = self
    }

    /// 列表视图
    lazy var listView = BaseListView().then {
        $0.delegate = self
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
        loadReasonData()
    }

    // MARK: - UI Setup

    override func configUI() {
        configView(title: "选择退货原因", bottomTitle: "确定退货")

        // 添加退款类型选择视图
        contentView.addSubview(retreatTypeSelectionView)

        // 添加列表视图
        contentView.addSubview(listView)

        // 设置约束
        setupConstraints()
    }

    /// 设置约束
    private func setupConstraints() {
        // 退款类型选择视图约束
        retreatTypeSelectionView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(0)
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }

        // 列表视图约束
        listView.snp.makeConstraints { make in
            make.top.equalTo(retreatTypeSelectionView.snp.bottom).offset(12)
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(330)
        }
    }

    // MARK: - Bindings

    override func setupBindings() {
        super.setupBindings()

        bottomButton.tapPublisher
            .sink { [weak self] _ in
                self?.handleConfirmAction()
            }
            .store(in: &cancellables)
    }

    // MARK: - 数据加载

    /// 加载退款原因数据
    private func loadReasonData() {
        let reasons = getReasonList(for: currentRetreatType)
        listView.setItems([reasons])
    }

    /// 获取退款原因列表
    private func getReasonList(for type: RetreatType) -> [ListItemConfig] {
        switch type {
        case .refundAndReturn:
            return [
                ListItemConfig(type: .retreatReason, identifier: "reason_1",data: "1",title: "少用优惠"),
                ListItemConfig(type: .retreatReason, identifier: "reason_2",data: "2", title: "协商一致退款"),
                ListItemConfig(type: .retreatReason, identifier: "reason_3",data: "3", title: "材质与描述不符"),
                ListItemConfig(type: .retreatReason, identifier: "reason_4",data: "4", title: "颜色/款式/大小/尺寸与描述不符"),
                ListItemConfig(type: .retreatReason, identifier: "reason_5",data: "5", title: "做工粗糙/有瑕疵"),
                ListItemConfig(type: .retreatReason, identifier: "reason_6",data: "6", title: "质量问题"),
                ListItemConfig(type: .retreatReason, identifier: "reason_7",data: "7", title: "少件（含缺少配件）"),
                ListItemConfig(type: .retreatReason, identifier: "reason_8",data: "8", title: "收到商品破损、污渍")
            ]
        case .refundOnly:
            return [
                ListItemConfig(type: .retreatReason, identifier: "refund_reason_1",data: "1", title: "协商一致退款"),
                ListItemConfig(type: .retreatReason, identifier: "refund_reason_2",data: "2", title: "未发货"),
            ]
        }
    }

    // MARK: - 事件处理

    /// 处理确认按钮点击
    private func handleConfirmAction() {
        // 检查是否有选择项
        guard let selectedIdentifier = listView.getSelectedSingleSelectIdentifier() else {
            // 显示提示信息
            showErrorMessage("请选择退货原因")
            return
        }

        // 获取选中项的数据
        let selectedItem = listView.getItem(identifier: selectedIdentifier)
        let selectedData = selectedItem.getData()

        // 构建返回数据
        let resultData: [String: Any] = [
            "retreat_type": currentRetreatType.rawValue,
            "retreat_type_title": currentRetreatType.title,
            "reason_identifier": selectedIdentifier,
            "reason_text": selectedData as? String ?? ""
        ]

        // 发送选择完成事件
        selectCompletePublisher.send(resultData)

        // 关闭弹窗
        dismiss(animated: true)
    }

    /// 显示错误信息
    private func showErrorMessage(_ message: String) {
        // 这里可以使用项目中的提示组件
        // 暂时使用简单的alert
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - RetreatTypeSelectionViewDelegate

extension ProductOrderRetreatReasonController: RetreatTypeSelectionViewDelegate {

    func retreatTypeSelectionView(_ view: RetreatTypeSelectionView, didSelectType type: RetreatType) {
        currentRetreatType = type

        // 更新标题
        let title = type == .refundAndReturn ? "选择退货原因" : "选择退款原因"
        titleLabel.text = title

        // 重新加载数据
        loadReasonData()
    }
}

// MARK: - BaseListViewDelegate

extension ProductOrderRetreatReasonController: BaseListViewDelegate {

    func listViewClick(_ listView: BaseListView, config: ListItemConfig) {
        // 处理退款原因选择项点击
        if config.type == .retreatReason {
            // 单选逻辑已在BaseListView中处理
            print("选择了退款原因: \(config.title)")
        }
    }

    func listViewUpdate(_ listView: BaseListView, with data: Any?) {
        // 数据更新回调
        print("列表数据更新: \(data ?? "nil")")
    }
}

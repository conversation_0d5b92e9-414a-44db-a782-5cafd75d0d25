//
//  ProductTrackingInfoController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/3.
//

class ProductTrackingInfoController: BasePresentController {

    /// 选择结果
    let selectCompletePublisher = PassthroughSubject<Bool, Never>()

    // MARK: - Properties

    /// ViewModel
    private let viewModel = ProductTrackingInfoViewModel()

    /// 物流信息
    lazy var orderLogistics = OrderLogistics()

    // MARK: - UI Components

    /// 快递信息头部视图
    private lazy var deliveryContentView = DeliveryContentView().then{
        $0.arrowButton.isHidden = true
    }

    /// 滚动视图
    private lazy var scrollView = UIScrollView().then {
        $0.backgroundColor = .clear
        $0.showsVerticalScrollIndicator = false
        $0.showsHorizontalScrollIndicator = false
    }

    /// 物流进度视图
    private lazy var progressView = LogisticsProgressView()



    // MARK: - 生命周期

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        configLayout()
        setupBindings()
        loadData()
    }

    // MARK: - UI Configuration

    override func configUI() {
        configView(title: "快递进度", bottomTitle: "确定")
        self.hideBottomBar()

        // 添加子视图
        contentView.addSubview(deliveryContentView)
        contentView.addSubview(scrollView)
        scrollView.addSubview(progressView)

        // 配置快递信息头部
        deliveryContentView.configTitle(
            express_company_txt: orderLogistics.express_company_txt,
            express_no: orderLogistics.express_no
        )
    }

    /// UI布局
    override func configLayout() {
        // 快递信息头部视图
        deliveryContentView.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
        }

        // 滚动视图
        scrollView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(deliveryContentView.snp.bottom).offset(12)
            make.bottom.equalToSuperview()
            make.height.equalTo(400)
        }

        // 物流进度视图
        progressView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(scrollView)
        }
      
    }

    // MARK: - Data Binding

    override func setupBindings() {
        super.setupBindings()

        // 监听物流轨迹数据变化
        viewModel.$trackItems
            .receive(on: DispatchQueue.main)
            .sink { [weak self] items in
                guard let self = self else { return }
                self.progressView.updateTrackItems(items)
            }
            .store(in: &cancellables)

    
      
    }

    // MARK: - Data Loading

    private func loadData() {
        viewModel.setLogistics(orderLogistics)
        viewModel.fetchLogisticsInfo()
    }
  
}


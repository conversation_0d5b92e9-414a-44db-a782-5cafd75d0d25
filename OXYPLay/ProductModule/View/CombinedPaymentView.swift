//
//  CombinedPaymentView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/25.
//

import UIKit
import Combine

/// 支付数据结构
struct PaymentData {
    let walletAmount: Float
    let thirdPartyAmount: Float
    let paymentType: String
}

/// 组合支付视图代理
protocol CombinedPaymentViewDelegate: AnyObject {
    /// 支付金额发生变化
    func paymentView(_ view: CombinedPaymentView, didUpdatePaymentAmount amount: Float)
}

/// 组合支付视图
class CombinedPaymentView: BaseView {
    
    // MARK: - Properties
    
    weak var delegate: CombinedPaymentViewDelegate?
    
    /// 订单总金额
    private var totalAmount: Float = 0.0
    
    /// 可用余额
    private var availableBalance: Float = 100.0
    
    /// 当前余额支付金额
    private var currentWalletAmount: Float = 0.0
    
    /// 当前第三方支付金额
    private var currentThirdPartyAmount: Float = 0.0
    
    /// 选中的第三方支付方式
    private var selectedThirdPartyPayment: String = "alipay" // 默认支付宝
    
    // MARK: - UI Components
    
    /// 余额输入容器
    private lazy var balanceContentView = UIView().then {
        $0.layer.cornerRadius = 16
        $0.layer.masksToBounds = true
        $0.backgroundColor = color_FFFFFF
    }
    
    /// 余额标签
    private lazy var balanceLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        $0.textColor = color_2B2C2F
        $0.text = "余额"
    }
    
    /// 可用余额标签
    private lazy var availableBalanceLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 11, weight: .regular)
        $0.textColor = color_2B2C2F72
        $0.text = "可用余额¥\(String(format: "%.2f", availableBalance))"
    }
    
    /// 全部转入按钮
    private lazy var allInButton = UIButton().then {
        $0.setTitle("全部转入", for: .normal)
        $0.setTitleColor(color_blue, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 11, weight: .regular)
    }
    
    /// 余额输入框
    private lazy var balanceInputField = UITextField().then {
        $0.placeholder = "输入您的余额数"
        $0.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        $0.textColor = color_2B2C2F
        $0.keyboardType = .decimalPad
        $0.textAlignment = .right
        $0.text = "¥ 输入您的余额数"
        $0.borderStyle = .none
        $0.backgroundColor = .clear
        $0.delegate = self
    }
    
    /// 支付方式选择容器
    private lazy var selectContentView = UIView().then {
        $0.layer.cornerRadius = 16
        $0.layer.masksToBounds = true
        $0.backgroundColor = color_FFFFFF
    }
    
    /// 剩余支付标签
    private lazy var remainingLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.textColor = color_2B2C2F
        $0.text = "还需支付 ￥"
        $0.textAlignment = .center
    }
    
    /// 剩余支付金额标签
    private lazy var remainingNumberLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 20, weight: .medium)
        $0.textColor = color_2B2C2F
        $0.text = String(format: "%.2f", totalAmount)
        $0.textAlignment = .center
    }
    
    /// 支付宝按钮
    private lazy var alipayButton = BaseButton().then {
        $0.setTitle("支付宝", for: .normal)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.setImage(UIImage(named: "baselist_pay_ali"), for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        $0.isSelected = true
    }

    /// 支付宝单选按钮
    private lazy var alipayRadioButton = UIButton().then {
        $0.setImage(UIImage(named: "baselist_single_normal"), for: .normal)
        $0.setImage(UIImage(named: "baselist_single_select"), for: .selected)
        $0.isSelected = true
    }

    /// 微信按钮
    private lazy var wechatButton = BaseButton().then {
        $0.setTitle("微信", for: .normal)
        $0.setImage(UIImage(named: "baselist_pay_wechat"), for: .normal)
        $0.setTitleColor(color_2B2C2F, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .regular)
    }

    /// 微信单选按钮
    private lazy var wechatRadioButton = UIButton().then {
        $0.setImage(UIImage(named: "baselist_single_normal"), for: .normal)
        $0.setImage(UIImage(named: "baselist_single_select"), for: .selected)
    }
    
    // MARK: - Lifecycle
    
    override func configUI() {
        setupBalanceSection()
        setupPaymentMethodSection()
        setupBindings()
    }
    
    override func setupBindings() {
        setupButtonBindings()
        setupTextFieldBindings()
    }
    
    // MARK: - UI Setup
    
    /// 设置余额输入区域
    private func setupBalanceSection() {
        // 余额输入容器
        addSubview(balanceContentView)
        balanceContentView.snp.makeConstraints { make in
            make.top.equalTo(0)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.height.equalTo(38)
        }
        
        // 余额标签
        balanceContentView.addSubview(balanceLabel)
        balanceLabel.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.centerY.equalToSuperview()
        }

        // 余额输入框
        balanceContentView.addSubview(balanceInputField)
        balanceInputField.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.centerY.equalToSuperview()
            make.left.equalTo(balanceLabel.snp.right).offset(12)
        }
                
        // 可用余额和全部转入按钮
        addSubview(availableBalanceLabel)
        addSubview(allInButton)

        availableBalanceLabel.snp.makeConstraints { make in
            make.left.equalTo(balanceContentView)
            make.top.equalTo(balanceContentView.snp.bottom).offset(8)
        }

        allInButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalTo(availableBalanceLabel)
        }
    }
    
    /// 设置支付方式选择区域
    private func setupPaymentMethodSection() {
        // 支付方式容器
        addSubview(selectContentView)
        selectContentView.snp.makeConstraints { make in
            make.top.equalTo(availableBalanceLabel.snp.bottom).offset(16)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalToSuperview()
        }
        
        // 剩余支付金额
        selectContentView.addSubview(remainingLabel)
        selectContentView.addSubview(remainingNumberLabel)
        
        remainingLabel.snp.makeConstraints { make in
            make.top.equalTo(18)
            make.right.equalTo(selectContentView.snp.centerX)
        }
        remainingNumberLabel.snp.makeConstraints { make in
            make.centerY.equalTo(remainingLabel)
            make.left.equalTo(remainingLabel.snp.right).offset(0)
        }

        setupPaymentOptions()
    }
    
    /// 设置支付选项
    private func setupPaymentOptions() {
        // 支付宝选项
        let alipayContainer = UIView()
        selectContentView.addSubview(alipayContainer)
        
        let line1 = UIView().then {
            $0.backgroundColor = UIColor(hexString: "#2B2C2F", transparency: 0.08)
        }
        alipayContainer.addSubview(line1)
        alipayContainer.addSubview(alipayButton)
        alipayContainer.addSubview(alipayRadioButton)

        alipayContainer.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.top.equalTo(remainingLabel.snp.bottom).offset(14)
            make.height.equalTo(40)
        }

        line1.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
            make.height.equalTo(0.5)
        }
        alipayButton.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        alipayRadioButton.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }

        // 微信选项
        let wechatContainer = UIView()
        selectContentView.addSubview(wechatContainer)
        
      
        wechatContainer.addSubview(wechatButton)
        wechatContainer.addSubview(wechatRadioButton)
        
        wechatContainer.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.top.equalTo(alipayContainer.snp.bottom)
            make.height.equalTo(40)
            make.bottom.equalToSuperview()
        }

        wechatButton.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        wechatRadioButton.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
    }

    // MARK: - Bindings

    /// 设置按钮绑定
    private func setupButtonBindings() {
        // 全部转入按钮
        allInButton.tapPublisher
            .sink { [weak self] _ in
                self?.transferAllBalance()
            }
            .store(in: &cancellables)

        // 支付宝选择
        alipayButton.tapPublisher
            .sink { [weak self] _ in
                self?.selectPaymentMethod("alipay")
            }
            .store(in: &cancellables)

        alipayRadioButton.tapPublisher
            .sink { [weak self] _ in
                self?.selectPaymentMethod("alipay")
            }
            .store(in: &cancellables)

        // 微信选择
        wechatButton.tapPublisher
            .sink { [weak self] _ in
                self?.selectPaymentMethod("wechat")
            }
            .store(in: &cancellables)

        wechatRadioButton.tapPublisher
            .sink { [weak self] _ in
                self?.selectPaymentMethod("wechat")
            }
            .store(in: &cancellables)
    }

    /// 设置文本框绑定
    private func setupTextFieldBindings() {
        // 使用UITextFieldDelegate处理输入变化，避免与textPublisher冲突
        // balanceInputField的delegate已经在UI组件定义时设置
    }

    // MARK: - Private Methods

    /// 全部转入余额
    private func transferAllBalance() {
        // 确保可用余额不为负数
        let safeAvailableBalance = max(0, availableBalance)
        let maxAmount = min(safeAvailableBalance, totalAmount)

        // 更新输入框显示
        balanceInputField.text = "¥ \(String(format: "%.2f", maxAmount))"

        // 更新内部状态
        currentWalletAmount = maxAmount
        updateRemainingAmount()

        print("全部转入: 可用余额=\(safeAvailableBalance), 订单金额=\(totalAmount), 转入金额=\(maxAmount)")
    }

    /// 处理余额输入变化
    private func handleBalanceInputChange(_ text: String?) {
        guard let text = text else {
            currentWalletAmount = 0.0
            updateRemainingAmount()
            return
        }

        // 提取数字部分，移除占位符文本和货币符号
        var numericString = text.replacingOccurrences(of: "¥", with: "")
            .replacingOccurrences(of: "输入您的余额数", with: "")
            .trimmingCharacters(in: .whitespaces)

        // 如果是空字符串，设置为0
        if numericString.isEmpty {
            currentWalletAmount = 0.0
            updateRemainingAmount()
            return
        }

        // 尝试转换为数字
        if let amount = Float(numericString) {
            // 确保金额不为负数
            let validAmount = max(0, amount)

            // 限制最大金额：不能超过可用余额和订单总金额
            let safeAvailableBalance = max(0, availableBalance)
            let maxAmount = min(validAmount, min(safeAvailableBalance, totalAmount))

            currentWalletAmount = maxAmount

            // 如果输入的金额超过了限制，更新输入框显示
            if validAmount != maxAmount {
                balanceInputField.text = "¥ \(String(format: "%.2f", maxAmount))"
            }

            print("输入金额处理: 输入=\(validAmount), 限制后=\(maxAmount), 可用余额=\(safeAvailableBalance)")
        } else {
            // 无法转换为数字，设置为0
            currentWalletAmount = 0.0
        }

        updateRemainingAmount()
    }

    /// 选择支付方式
    private func selectPaymentMethod(_ method: String) {
        // 检查第三方支付是否可用
        let isThirdPartyDisabled = (availableBalance >= totalAmount) &&
                                  (abs(currentWalletAmount - totalAmount) < 0.01)

        // 如果第三方支付被禁用，则不允许选择
        if isThirdPartyDisabled && (method == "alipay" || method == "wechat") {
            print("第三方支付已禁用，无法选择 \(method)")
            return
        }

        selectedThirdPartyPayment = method

        alipayRadioButton.isSelected = (method == "alipay")
        wechatRadioButton.isSelected = (method == "wechat")

        print("选择支付方式: \(method)")
    }

    /// 更新剩余支付金额
    private func updateRemainingAmount() {
        currentThirdPartyAmount = max(0, totalAmount - currentWalletAmount)
        remainingNumberLabel.text = String(format: "%.2f", currentThirdPartyAmount)

        // 根据支付金额情况更新第三方支付选项的可用状态
        updateThirdPartyPaymentAvailability()

        // 通知代理金额变化，传递实际的支付总额
        let actualTotalAmount = currentWalletAmount + currentThirdPartyAmount
        delegate?.paymentView(self, didUpdatePaymentAmount: currentThirdPartyAmount)

        print("更新剩余金额: 余额支付=\(currentWalletAmount), 第三方支付=\(currentThirdPartyAmount), 总计=\(actualTotalAmount)")
    }

    /// 更新第三方支付选项的可用状态
    /// 当余额足够覆盖全部金额且用户输入的余额等于到手价时，第三方支付不可选
    private func updateThirdPartyPaymentAvailability() {
        // 判断是否需要禁用第三方支付
        // 条件：可用余额足够覆盖到手价 && 用户输入的余额金额等于到手价
        let shouldDisableThirdParty = (availableBalance >= totalAmount) &&
                                     (abs(currentWalletAmount - totalAmount) < 0.01)

        // 更新支付宝按钮状态
        alipayButton.isEnabled = !shouldDisableThirdParty
        alipayRadioButton.isEnabled = !shouldDisableThirdParty
        alipayButton.alpha = shouldDisableThirdParty ? 0.5 : 1.0
        alipayRadioButton.alpha = shouldDisableThirdParty ? 0.5 : 1.0

        // 更新微信按钮状态
        wechatButton.isEnabled = !shouldDisableThirdParty
        wechatRadioButton.isEnabled = !shouldDisableThirdParty
        wechatButton.alpha = shouldDisableThirdParty ? 0.5 : 1.0
        wechatRadioButton.alpha = shouldDisableThirdParty ? 0.5 : 1.0

        // 如果第三方支付被禁用且当前选中的是第三方支付，则取消选中状态
        if shouldDisableThirdParty {
            alipayRadioButton.isSelected = false
            wechatRadioButton.isSelected = false
            // 不需要设置其他支付方式为选中，因为此时是纯余额支付
        } else if currentThirdPartyAmount > 0 {
            // 如果需要第三方支付且当前没有选中任何第三方支付方式，默认选择支付宝
            if !alipayRadioButton.isSelected && !wechatRadioButton.isSelected {
                selectPaymentMethod("alipay")
            }
        }

        print("第三方支付可用状态: \(!shouldDisableThirdParty), 可用余额: \(availableBalance), 到手价: \(totalAmount), 余额支付: \(currentWalletAmount)")
    }

    // MARK: - Public Methods

    /// 配置支付视图
    /// - Parameters:
    ///   - totalAmount: 订单总金额
    ///   - availableBalance: 可用余额
    func configure(totalAmount: Float, availableBalance: Float) {
        self.totalAmount = totalAmount
        self.availableBalance = max(0, availableBalance) // 确保余额不为负数

        // 重置输入状态
        currentWalletAmount = 0.0
        currentThirdPartyAmount = totalAmount

        // 更新UI显示
        updateAvailableBalance(self.availableBalance)
        updateTotalAmount(totalAmount)

        // 根据余额情况决定默认支付方式
        if self.availableBalance >= totalAmount {
            // 余额足够，默认不选择第三方支付，等待用户输入余额金额
            alipayRadioButton.isSelected = false
            wechatRadioButton.isSelected = false
            selectedThirdPartyPayment = "alipay" // 保持默认值，但不选中
        } else {
            // 余额不足，默认选择支付宝
            selectPaymentMethod("alipay")
        }

        // 重置输入框
        balanceInputField.text = "¥ 输入您的余额数"

        print("配置支付视图: 总金额=\(totalAmount), 可用余额=\(self.availableBalance)")
    }

    /// 更新订单总金额
    func updateTotalAmount(_ amount: Float) {
        totalAmount = amount
        updateRemainingAmount()
    }

    /// 更新可用余额
    func updateAvailableBalance(_ balance: Float) {
        // 确保余额不为负数，如果小于0则设置为0
        availableBalance = max(0.0, balance)
        availableBalanceLabel.text = "可用余额¥\(String(format: "%.2f", availableBalance))"

        // 余额更新后，重新计算第三方支付的可用状态
        updateThirdPartyPaymentAvailability()

        print("更新可用余额: 原始值=\(balance), 处理后=\(availableBalance)")
    }

    /// 获取支付数据
    func getPaymentData() -> PaymentData {
        return PaymentData(
            walletAmount: currentWalletAmount,
            thirdPartyAmount: currentThirdPartyAmount,
            paymentType: selectedThirdPartyPayment
        )
    }
}

// MARK: - UITextFieldDelegate

extension CombinedPaymentView: UITextFieldDelegate {

    func textFieldDidBeginEditing(_ textField: UITextField) {
        if textField == balanceInputField {
            // 清除占位符文本
            if textField.text == "¥ 输入您的余额数" {
                textField.text = "¥ "
            }
        }
    }

    func textFieldDidEndEditing(_ textField: UITextField) {
        if textField == balanceInputField {
            // 如果为空，恢复占位符
            let cleanText = textField.text?.replacingOccurrences(of: "¥", with: "").trimmingCharacters(in: .whitespaces) ?? ""
            if cleanText.isEmpty {
                textField.text = "¥ 输入您的余额数"
                currentWalletAmount = 0.0
                updateRemainingAmount()
            }
        }
    }

    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        if textField == balanceInputField {
            // 获取当前文本
            let currentText = textField.text ?? ""
            let newText = (currentText as NSString).replacingCharacters(in: range, with: string)

            // 确保始终以"¥ "开头
            if !newText.hasPrefix("¥ ") {
                if newText.isEmpty || newText == "¥" {
                    textField.text = "¥ "
                    return false
                }
            }

            // 只允许数字和小数点
            let allowedCharacters = CharacterSet(charactersIn: "0123456789.")
            let characterSet = CharacterSet(charactersIn: string)

            if !allowedCharacters.isSuperset(of: characterSet) && !string.isEmpty {
                return false
            }

            // 延迟处理输入变化，确保文本框已更新
            DispatchQueue.main.async { [weak self] in
                self?.handleBalanceInputChange(newText)
            }

            return true
        }

        return true
    }
}

//
//  LogisticsProgressView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/3.
//

import UIKit
import SnapKit
import Then

/// 物流进度视图
/// 使用StackView实现物流轨迹的时间线展示
class LogisticsProgressView: BaseView {
    
    // MARK: - Properties
    
    /// 物流轨迹数据
    private var trackItems: [LogisticsTrackItem] = []
    
    // MARK: - UI Components
    
    /// 主容器StackView
    private lazy var mainStackView = UIStackView().then {
        $0.axis = .vertical
        $0.spacing = 0
        $0.alignment = .fill
        $0.distribution = .fill
    }
    
    // MARK: - UI Configuration
    
    override func configUI() {
        backgroundColor = .clear
        addSubview(mainStackView)
    }
    
    override func configLayout() {
        mainStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - Public Methods
    
    /// 更新物流轨迹数据
    /// - Parameter items: 物流轨迹数据数组
    func updateTrackItems(_ items: [LogisticsTrackItem]) {
        trackItems = items
        setupProgressItems()
    }
    
    // MARK: - Private Methods
    
    /// 设置进度项
    private func setupProgressItems() {
        // 清除之前的视图
        mainStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        
        // 按时间倒序排列，最新的在最上面
        let sortedItems = trackItems.sorted { item1, item2 in
            return item1.time > item2.time
        }
        
        for (index, item) in sortedItems.enumerated() {
            let isFirst = index == 0
            let isLast = index == sortedItems.count - 1
            
            let progressItemView = createProgressItemView(
                item: item,
                isFirst: isFirst,
                isLast: isLast
            )
            
            mainStackView.addArrangedSubview(progressItemView)
        }
    }
    
    /// 创建单个进度项视图
    /// - Parameters:
    ///   - item: 物流轨迹项
    ///   - isFirst: 是否是第一项（最新）
    ///   - isLast: 是否是最后一项
    /// - Returns: 进度项视图
    private func createProgressItemView(
        item: LogisticsTrackItem,
        isFirst: Bool,
        isLast: Bool
    ) -> UIView {
        let containerView = UIView()
        
        // 节点图片
        let nodeImageView = UIImageView().then {
            let imageName = isFirst ? "prodcut_order_progress_completed" : "prodcut_order_progress_inprogress"
            $0.image = UIImage(named: imageName)
            $0.contentMode = .scaleAspectFit
        }
        
        // 连接线（上半部分）
        let topLineView = UIView().then {
            $0.backgroundColor = isFirst ? .clear : color_progress_blue_light
        }
        
        // 连接线（下半部分）
        let bottomLineView = UIView().then {
            $0.backgroundColor = isLast ? .clear : color_progress_blue_light
        }
    
        // 日期标签
        let dateLabel = UILabel().then {
            $0.text = item.ftime
            $0.textColor = color_2B2C2F64
            $0.font = UIFont.systemFont(ofSize: 12, weight: .regular)
            $0.textAlignment = .right
        }
        
        // 标题标签
        let titleLabel = UILabel().then {
            $0.text = "当前快递信息位置"
            $0.textColor = isFirst ? color_blue : color_2B2C2F
            $0.font = UIFont.systemFont(ofSize: 14, weight: .medium)
            $0.numberOfLines = 0
        }
        
        // 描述标签
        let descriptionLabel = UILabel().then {
            $0.text = item.context
            $0.textColor = color_2B2C2F80
            $0.font = UIFont.systemFont(ofSize: 12, weight: .regular)
            $0.numberOfLines = 0
        }
        
        // 添加子视图
        containerView.addSubview(topLineView)
        containerView.addSubview(nodeImageView)
        containerView.addSubview(bottomLineView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(descriptionLabel)
        
        containerView.addSubview(dateLabel)
        // 设置约束
        // 上连接线
        topLineView.snp.makeConstraints { make in
            make.centerX.equalTo(nodeImageView)
            make.width.equalTo(2)
            make.top.equalToSuperview()
            make.bottom.equalTo(nodeImageView.snp.top).offset(-2)
        }
        
    
        nodeImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.top.equalToSuperview().offset(12)
            make.width.height.equalTo(12)

        }
        // 下连接线
        bottomLineView.snp.makeConstraints { make in
            make.centerX.equalTo(nodeImageView)
            make.width.equalTo(2)
            make.top.equalTo(nodeImageView.snp.bottom).offset(2)
            make.bottom.equalToSuperview()
        }
        
        // 时间
        dateLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalTo(titleLabel)
        }
        
        
        // 标题标签
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(nodeImageView.snp.right).offset(12)
            make.right.lessThanOrEqualTo(dateLabel.snp.left).offset(-12)
            make.top.equalTo(nodeImageView)
        }
        
        // 描述标签
        descriptionLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.right.equalTo(-12)
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.bottom.equalToSuperview().offset(-12)
        }
        
        return containerView
    }
}

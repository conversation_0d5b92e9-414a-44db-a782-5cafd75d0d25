//
//  DetailProductImagesView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/15.
//

class DetailProductImagesView: BaseView {
    
    // MARK: - 属性
    
    private var imageUrls: [String] = []
    
    // MARK: - UI组件
    
    private lazy var containerView = UIView()
    
    private lazy var pageControl = UILabel().then {
        $0.backgroundColor = UIColor(hexString: "000000", transparency: 0.48)
        $0.textColor = .white
        $0.font = .systemFont(ofSize: 12, weight: .regular)
        $0.layer.cornerRadius = 12
        $0.layer.masksToBounds = true
        $0.textAlignment = .center
    }
    private lazy var tagLabel = UILabel().then {
        $0.backgroundColor = UIColor(hexString: "000000", transparency: 0.48)
        $0.textColor = .white
        $0.font = .systemFont(ofSize: 12, weight: .regular)
        $0.layer.cornerRadius = 12
        $0.layer.masksToBounds = true
        $0.textAlignment = .center
    }
    private lazy var locationButton = BaseButton().then {
        $0.backgroundColor = UIColor(hexString: "000000", transparency: 0.48)
        $0.setTitleColor(.white, for: .normal)
        $0.titleLabel?.font = .systemFont(ofSize: 12, weight: .regular)
        $0.isRounded = true
        $0.setImage(UIImage(named: "assembly_location")?.withTintColor(.white), for: .normal)
        $0.spacing = 5
    }
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
        
        let collection = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collection.backgroundColor = .white
        collection.showsHorizontalScrollIndicator = false
        collection.isPagingEnabled = true
        collection.delegate = self
        collection.dataSource = self
        collection.register(ProductImageCell.self, forCellWithReuseIdentifier: "ProductImageCell")
        return collection
    }()
    

    
    // MARK: - UI设置
    
    override func configUI()  {
        backgroundColor = color_F3F6F7
        
        self.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        containerView.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(kScreenWidth)
            make.bottom.equalToSuperview().priority(.low)
        }
        
        containerView.addSubview(pageControl)
        pageControl.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.bottom.equalTo(collectionView.snp.bottom).offset(-10)
            make.height.equalTo(24)
            make.width.equalTo(45)
        }
        containerView.addSubview(tagLabel)
        tagLabel.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.top.equalTo(12)
            make.height.equalTo(24)
            make.width.equalTo(45)
        }
        containerView.addSubview(locationButton)
        locationButton.snp.makeConstraints { make in
            make.left.equalTo(12)
            make.bottom.equalTo(collectionView.snp.bottom).offset(-10)
            make.height.equalTo(24)
            make.width.equalTo(locationButton.intrinsicContentSize.width + 10)
        }
    }
    
    // MARK: - 配置方法
    
    func configure(with model: HomeDetailModel) {
        self.imageUrls = model.imgUrls.map{$0.url}
        if imageUrls.count > 1 {
            pageControl.text = "1/\(imageUrls.count)"
        }else{
            pageControl.text = "0"
        }
        tagLabel.text = model.type_text
        tagLabel.isHidden = model.type_text.count == 0
        locationButton.setTitle(model.location, for: .normal)
        locationButton.snp.updateConstraints { make in
            make.width.equalTo(locationButton.intrinsicContentSize.width + 15)
        }
        
        locationButton.isHidden = model.location.count == 0
        tagLabel.snp.updateConstraints { make in
            make.width.equalTo(tagLabel.intrinsicContentSize.width + 15)
        }
        collectionView.reloadData()
    }
    func configureProduct(with images: [String]) {
        tagLabel.isHidden = true
        locationButton.isHidden = true
      
        imageUrls = images
        if imageUrls.isEmpty{
            pageControl.text = "0"
        }else{
            pageControl.text = "1/\(imageUrls.count)"
        }
        collectionView.reloadData()
    }
}

// MARK: - UICollectionViewDelegate, UICollectionViewDataSource

extension DetailProductImagesView: UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return imageUrls.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ProductImageCell", for: indexPath) as! ProductImageCell
        let imageUrl = imageUrls[indexPath.item]
        cell.configure(with: imageUrl, cornerRadius: 0)
        return cell
    }
    
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        let pageWidth = scrollView.frame.width
        let currentPage = Int((scrollView.contentOffset.x + pageWidth / 2) / pageWidth)
        pageControl.text = "\(currentPage+1)/\(imageUrls.count)"

    }
}

// MARK: - UICollectionViewDelegateFlowLayout

extension DetailProductImagesView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: collectionView.frame.width, height: collectionView.frame.height)
    }
}


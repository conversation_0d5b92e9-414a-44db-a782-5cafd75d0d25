//
//  DetailProductInfoTableCell.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/4.
//

import UIKit
import SnapKit
import Then
import Kingfisher

// MARK: - 代理协议
protocol DetailProductInfoTableCellDelegate: AnyObject {
    /// 商品变体选择发生变化
    /// - Parameter selectedIndex: 选中的变体索引
    func didSelectVariant(at selectedIndex: Int)
}

class DetailProductInfoTableCell: UITableViewCell {
    // MARK: - 代理
    weak var delegate: DetailProductInfoTableCellDelegate?

    // MARK: - UI组件
    
    // 主容器
    private lazy var containerStackView = UIStackView().then {
        $0.axis = .vertical
        $0.spacing = 10
        $0.distribution = .fill
        $0.alignment = .fill
    }
    
    // 商品图片轮播
    private lazy var imageCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 12
        layout.minimumInteritemSpacing = 12
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.register(ProductImageCell.self, forCellWithReuseIdentifier: "ProductImageCell")
        collectionView.dataSource = self
        collectionView.delegate = self
        return collectionView
    }()
    
    // 价格信息区域
    private lazy var infoView = UIView()
    
    private lazy var priceLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 11, weight: .regular)
        $0.textColor = color_red
        $0.text = "¥2223.00"
    }
    
    private lazy var originalPriceLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 13,weight: .regular)
        $0.textColor = UIColor(hexString: "2B2C2F", transparency: 0.48)
    }
    
    private lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.textColor = color_2B2C2F
        $0.numberOfLines = 0
        $0.text = "AK EMBARK GORE-TAX 2L 滑雪服"
    }
    
    private lazy var freeShippingLabel = BasePaddingLabel().then {
        $0.font = UIFont.systemFont(ofSize: 11,weight: .regular)
        $0.textColor = color_red
        $0.backgroundColor = UIColor(hexString: "FF0000", transparency: 0.08)
        $0.textAlignment = .center
        $0.layer.cornerRadius = 4
        $0.layer.masksToBounds = true
        $0.setHorizontalPadding(4)
    }
    
    
    private lazy var bannerImageView = UIImageView().then {
        $0.contentMode = .scaleAspectFill
        $0.clipsToBounds = true
        $0.backgroundColor = .red
    }
    
    // 商品详情图片
    private lazy var detailImagesView = UIStackView().then {
        $0.axis = .vertical
        $0.spacing = 0
        $0.distribution = .fill
        $0.alignment = .fill
    }
    //顶部滚动图
    private lazy var productImagesView = DetailProductImagesView()
    
    // MARK: - 初始化
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        selectionStyle = .none
        contentView.addSubview(containerStackView)
        contentView.addSubview(productImagesView)

        // 添加子视图到容器
        containerStackView.addArrangedSubview(imageCollectionView)
        containerStackView.addArrangedSubview(infoView)
        containerStackView.addArrangedSubview(titleLabel)
        containerStackView.addArrangedSubview(bannerImageView)
        containerStackView.addArrangedSubview(detailImagesView)
        
        // 设置价格信息区域
        infoView.addSubview(priceLabel)
        infoView.addSubview(originalPriceLabel)
        infoView.addSubview(freeShippingLabel)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        productImagesView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
        }
        containerStackView.snp.makeConstraints { make in
            make.top.equalTo(productImagesView.snp.bottom).offset(12)
            make.left.equalTo(12)
            make.right.bottom.equalTo(-12)
        }
        
        // 商品图片轮播高度
        imageCollectionView.snp.makeConstraints { make in
            make.height.equalTo(60)
        }
        
        // 价格信息区域
        priceLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview()
        }
        
        originalPriceLabel.snp.makeConstraints { make in
            make.centerY.equalTo(priceLabel)
            make.left.equalTo(priceLabel.snp.right).offset(8)
        }
        
        freeShippingLabel.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.height.equalTo(20)
            make.bottom.top.equalToSuperview()
        }
        
        // Banner区域
        bannerImageView.snp.makeConstraints { make in
            make.height.equalTo(60)
        }
        
        
    }
    
    // MARK: - 数据更新
    private var model = ProductDetailModel()
    func configureProduct(with model: ProductDetailModel) {
        self.model = model
        titleLabel.text = model.product.name
        freeShippingLabel.text = model.product.postage_type_text
        bannerImageView.setImage(url: model.product.cover_image)

        detailImagesView.subviews.forEach{$0.removeFromSuperview()}
        model.product.img_urls.forEach { url in
            let view = UIImageView()
            view.contentMode = .scaleAspectFill
            detailImagesView.addArrangedSubview(view)
            view.snp.makeConstraints { make in
                make.height.equalTo(view.snp.width).multipliedBy(1)
            }
            view.setImage(url: url)
          
        }
        reloadImages()
    }
    func reloadImages(){
        // 刷新商品图片轮播
        imageCollectionView.reloadData()
        
        guard let selectedModel = model.variants.data.filter({ $0.isSelect }).first else {
            return
        }
        //刷新头部banner
        productImagesView.configureProduct(with:selectedModel.images)
        priceLabel.text = model.getcurrentQuantityPrice().formattedPrice
        originalPriceLabel.setStrikethroughText(model.getOrigin_price())
    }
}

// MARK: - UICollectionViewDataSource, UICollectionViewDelegate

extension DetailProductInfoTableCell: UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return model.variants.data.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ProductImageCell", for: indexPath) as! ProductImageCell
        cell.configure(with: model.variants.data[indexPath.row].cover_image,isSelect: model.variants.data[indexPath.row].isSelect)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 60, height:60)
    }
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath){
        // 通过代理通知控制器处理变体选择
        delegate?.didSelectVariant(at: indexPath.row)
    }
}

// MARK: - 商品图片Cell

class ProductImageCell: UICollectionViewCell {
    
    private lazy var imageView = UIImageView().then {
        $0.contentMode = .scaleAspectFill
        $0.clipsToBounds = true
        $0.backgroundColor = .random
        $0.layer.borderColor = color_blue.cgColor
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        contentView.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func configure(with imageUrl: String,cornerRadius:CGFloat = 12,isSelect:Bool = false) {
        imageView.setImage(url:imageUrl)
        imageView.layer.cornerRadius = cornerRadius
        if isSelect {
            imageView.layer.borderWidth = 1
        }else{
            imageView.layer.borderWidth = 0
        }
    }
}

//
//  ProductCommentItemTableCell.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/14.
//

class ProductCommentItemTableCell: UITableViewCell {
    
    // MARK: - UI组件
    private lazy var model = CommentModel()

    private lazy var containerView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.masksToBounds = true
        $0.layer.cornerRadius = 16
    }
    
    private lazy var avatarImageView = UIImageView().then {
        $0.contentMode = .scaleAspectFill
        $0.clipsToBounds = true
        $0.layer.cornerRadius = 16
    }
    
    private lazy var nameLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        $0.textColor = UIColor(hexString:"3D3E40", transparency: 0.72)
    }
    
    
    private lazy var contentLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 13,weight: .regular)
        $0.textColor = UIColor(hexString:"3D3E40", transparency: 1)
        $0.numberOfLines = 0
    }
    private lazy var styleLabel = BasePaddingLabel().then {
        $0.font = UIFont.systemFont(ofSize: 11,weight: .regular)
        $0.textColor = UIColor(hexString:"2B2C2F", transparency: 0.72)
        $0.numberOfLines = 0
        $0.textAlignment = .center
        $0.backgroundColor = UIColor(hexString:"788092", transparency: 0.08)
        $0.setPadding(horizontal: 4, vertical: 0)
        $0.cornerRadius = 10
    }
    /// 图片容器视图
    private lazy var imagesContainerView: UIStackView = {
        let view = UIStackView()
        view.spacing = 16
        view.distribution = .fillEqually
        view.axis = .horizontal
        return view
    }()
    // MARK: - 初始化
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        selectionStyle = .none
        contentView.backgroundColor = color_F6F8F9
        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.top.equalTo(0)
            make.left.right.equalTo(0)
            make.bottom.equalTo(-12)
        }
        
        containerView.addSubview(avatarImageView)
        avatarImageView.snp.makeConstraints { make in
            make.top.equalTo(12)
            make.left.equalTo(12)
            make.width.height.equalTo(32)
        }
        
       
        // 然后再添加nameLabel并设置与likeButton的约束
        containerView.addSubview(nameLabel)
        nameLabel.snp.makeConstraints { make in
            make.centerY.equalTo(avatarImageView)
            make.left.equalTo(avatarImageView.snp.right).offset(8)
        }
        containerView.addSubview(styleLabel)
        styleLabel.snp.makeConstraints { make in
            make.centerY.equalTo(avatarImageView)
            make.left.equalTo(nameLabel.snp.right).offset(8)
            make.height.equalTo(20)
            make.right.lessThanOrEqualTo(containerView).offset(-10)
        }
        
        containerView.addSubview(contentLabel)
        contentLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView.snp.bottom).offset(8)
            make.left.equalTo(avatarImageView)
            make.right.equalToSuperview().offset(-15)
        }
        containerView.addSubview(imagesContainerView)
        // 图片容器
        imagesContainerView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.top.equalTo(contentLabel.snp.bottom).offset(12)
            make.right.lessThanOrEqualTo(12)
            make.bottom.equalTo(-12)
        }
                
    }
    
    // MARK: - 配置方法
    
    func configure(with comment: CommentModel) {
        model = comment
        nameLabel.text = comment.user.nickname
        contentLabel.text = comment.content
        styleLabel.text = comment.spec_value_text
        avatarImageView.setImage(url: comment.user.avatar)
        
        imagesContainerView.subviews.forEach{$0.removeFromSuperview()}
        let imageArray = Array(comment.images.prefix(2))
        if imageArray.isEmpty{
            imagesContainerView.snp.updateConstraints { make in
                make.top.equalTo(contentLabel.snp.bottom).offset(0)
            }
        }else{
            imagesContainerView.snp.updateConstraints { make in
                make.top.equalTo(contentLabel.snp.bottom).offset(12)
            }
            imagesContainerView.isHidden = false
            imageArray.forEach { url in
                let imageView = UIImageView()
                imageView.backgroundColor = .random
                imageView.layer.cornerRadius = 5
                imageView.masksToBounds = true
                imageView.setImage(url: url)
                imagesContainerView.addArrangedSubview(imageView)
                imageView.snp.makeConstraints { make in
                    make.width.height.equalTo(100)
                }
            }
        }
    }
}
class ProductDetailCommentItemTableCell: UITableViewCell {
    
    // MARK: - UI组件
    private lazy var model = CommentModel()

    private lazy var containerView = UIView()
    
    private lazy var avatarImageView = UIImageView().then {
        $0.contentMode = .scaleAspectFill
        $0.clipsToBounds = true
        $0.layer.cornerRadius = 16
    }
    
    private lazy var nameLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        $0.textColor = UIColor(hexString:"3D3E40", transparency: 0.72)
    }
    
    
    private lazy var contentLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 13,weight: .regular)
        $0.textColor = UIColor(hexString:"3D3E40", transparency: 1)
        $0.numberOfLines = 0
    }
    private lazy var styleLabel = BasePaddingLabel().then {
        $0.font = UIFont.systemFont(ofSize: 11,weight: .regular)
        $0.textColor = UIColor(hexString:"2B2C2F", transparency: 0.72)
        $0.numberOfLines = 0
        $0.textAlignment = .center
        $0.backgroundColor = UIColor(hexString:"788092", transparency: 0.08)
        $0.setHorizontalPadding(4)
        $0.cornerRadius = 10
    }
    
    // MARK: - 初始化
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        selectionStyle = .none
        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 0, left:12, bottom: 21, right: 12))
        }
        
        containerView.addSubview(avatarImageView)
        avatarImageView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.equalToSuperview()
            make.width.height.equalTo(32)
        }
        
       
        // 然后再添加nameLabel并设置与likeButton的约束
        containerView.addSubview(nameLabel)
        nameLabel.snp.makeConstraints { make in
            make.centerY.equalTo(avatarImageView)
            make.left.equalTo(avatarImageView.snp.right).offset(8)
        }
        containerView.addSubview(styleLabel)
        styleLabel.snp.makeConstraints { make in
            make.centerY.equalTo(avatarImageView)
            make.left.equalTo(nameLabel.snp.right).offset(8)
            make.height.equalTo(20)
            make.right.lessThanOrEqualTo(containerView).offset(-10)
        }
        
        containerView.addSubview(contentLabel)
        contentLabel.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(7)
            make.left.equalTo(nameLabel)
            make.right.equalToSuperview().offset(-15)
            make.bottom.equalToSuperview()
        }
                
    }
    
    // MARK: - 配置方法
    
    func configure(with comment: CommentModel) {
        model = comment
        nameLabel.text = comment.user.nickname
        contentLabel.text = comment.content
        styleLabel.text = comment.spec_value_text
        avatarImageView.setImage(url: comment.user.avatar)
    }
}

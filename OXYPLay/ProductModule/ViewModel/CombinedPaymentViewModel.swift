//
//  CombinedPaymentViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/3.
//

import Foundation
import Combine

/// 组合支付ViewModel
/// 负责管理余额获取、支付接口调用等支付相关逻辑
class CombinedPaymentViewModel: BaseViewModel {
    
    // MARK: - Published Properties
    
    /// 钱包概览数据
    @Published var walletSummary: WalletSummaryModel?

    /// 可用余额（处理后的非负值）
    @Published var availableBalance: Float = 0.0
    
    /// 支付响应结果
    @Published var paymentResult: PaymentResponse?
    
    /// 支付成功状态
    @Published var paymentSuccess: Bool = false
    
    /// 支付流程状态
    @Published var paymentFlowState: PaymentFlowState = .idle
    
    /// 支付错误信息
    @Published var paymentError: String?
    
    // MARK: - Private Properties
    
    /// 当前订单ID
    private var currentOrderId: Int = 0
    
    /// 当前支付方式
    private var currentPaymentMethod: String = ""
    
    /// 余额支付金额
    private var walletAmount: Float = 0.0
    
    /// 第三方支付金额
    private var thirdPartyAmount: Float = 0.0
    
    /// 组合支付时的第三方支付方式
    private var combineThirdPartyPaymentMethod: String = ""
    
    // MARK: - Initialization
    
    override init() {
        super.init()
        setupPaymentNotificationObserver()
    }

    /// 设置支付通知监听
    private func setupPaymentNotificationObserver() {
        // 监听支付结果不明确的通知
        NotificationCenter.default.publisher(for: NSNotification.Name("PaymentResultUnclear"))
            .sink { [weak self] _ in
                self?.handlePaymentResultUnclear()
            }
            .store(in: &cancellables)
    }

    /// 处理支付结果不明确的情况
    private func handlePaymentResultUnclear() {
        print("支付结果不明确，可能需要用户手动确认")
        // 这里可以设置一个状态，让UI层显示"支付结果确认中"的提示
        paymentFlowState = .waitingThirdPartyPayment
        paymentError = "支付结果确认中，请稍候..."
    }
    
    // MARK: - Public Methods
    
    /// 获取钱包余额
    func fetchWalletBalance() {
        let request = WalletSummaryRequest()

        requestModel(WalletService.summary(params: request), type: WalletSummaryModel.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case let .failure(error) = completion {
                        print("获取余额失败: \(error.localizedDescription)")
                        // 获取失败时设置默认值
                        self?.handleWalletBalanceResult(summary: nil)
                    }
                },
                receiveValue: { [weak self] summary in
                    self?.handleWalletBalanceResult(summary: summary)
                }
            )
            .store(in: &cancellables)
    }

    /// 处理余额获取结果
    private func handleWalletBalanceResult(summary: WalletSummaryModel?) {
        self.walletSummary = summary

        // 处理余额数据，确保不小于0
        let originalAmount = summary?.available_amount ?? 0.0
        let processedAmount = max(0.0, originalAmount)
        self.availableBalance = processedAmount

        print("获取余额结果: 原始余额=\(originalAmount), 处理后余额=\(processedAmount)")
    }
    
    /// 设置支付参数
    /// - Parameters:
    ///   - walletAmount: 余额支付金额
    ///   - thirdPartyAmount: 第三方支付金额
    ///   - paymentMethod: 支付方式
    ///   - thirdPartyMethod: 第三方支付方式（组合支付时使用）
    func setPaymentParameters(walletAmount: Float, thirdPartyAmount: Float, paymentMethod: String, thirdPartyMethod: String = "") {
        self.walletAmount = walletAmount
        self.thirdPartyAmount = thirdPartyAmount
        self.currentPaymentMethod = paymentMethod
        self.combineThirdPartyPaymentMethod = thirdPartyMethod
        
        print("设置支付参数 - 余额: ¥\(walletAmount), 第三方: ¥\(thirdPartyAmount), 方式: \(paymentMethod)")
    }
    
    /// 发起支付请求
    /// - Parameters:
    ///   - orderId: 订单ID
    ///   - completion: 完成回调
    func initiatePayment(orderId: Int, completion: @escaping (Bool, String) -> Void) {
        currentOrderId = orderId
        paymentFlowState = .initiatingPayment
        
        // 根据支付方式确定pay_method参数
        let payMethod = getPayMethodString()
        let paymentRequest = PaymentRequest(order_id: orderId, pay_method: payMethod)
        
        requestModel(OrderService.pay(params: paymentRequest), type: PaymentResponse.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionResult in
                    switch completionResult {
                    case .failure(let error):
                        self?.paymentFlowState = .paymentFailed(error: error.localizedDescription)
                        completion(false, error.localizedDescription)
                    case .finished:
                        break
                    }
                },
                receiveValue: { [weak self] response in
                    self?.paymentResult = response
                    self?.handlePaymentResponse(response, completion: completion)
                }
            )
            .store(in: &cancellables)
    }
    
    /// 通知服务器支付成功
    /// - Parameters:
    ///   - orderId: 订单ID
    ///   - tradeNo: 第三方支付交易号（可选）
    ///   - completion: 完成回调
    func notifyPaymentSuccess(orderId: Int, tradeNo: String? = nil, completion: @escaping (Bool, String) -> Void) {
        paymentFlowState = .notifyingPaymentSuccess
        
        let payMethod = getPayMethodString()
        let notifyRequest = PaymentNotifyRequest(
            order_id: orderId,
            pay_method: payMethod,
            trade_no: tradeNo
        )
        
        requestModel(OrderService.paymentNotify(params: notifyRequest), type: ResponseModel.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completionResult in
                    switch completionResult {
                    case .failure(let error):
                        self?.paymentFlowState = .paymentFailed(error: error.localizedDescription)
                        completion(false, error.localizedDescription)
                    case .finished:
                        break
                    }
                },
                receiveValue: { [weak self] response in
                    if response.code == 200 {
                        self?.paymentFlowState = .paymentCompleted(orderId: orderId)
                        self?.paymentSuccess = true
                        completion(true, "支付成功")
                    } else {
                        self?.paymentFlowState = .paymentFailed(error: response.message)
                        completion(false, response.message)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    /// 处理第三方支付
    /// - Parameter orderId: 订单ID
    func processThirdPartyPayment(orderId: Int) {
        paymentFlowState = .waitingThirdPartyPayment
        
        switch currentPaymentMethod {
        case PaymentMethodIdentifiers.alipay:
            handleAlipayPayment(orderId: orderId)
        case PaymentMethodIdentifiers.wechat:
            handleWechatPayment(orderId: orderId)
        case PaymentMethodIdentifiers.mix:
            handleCombinePayment(orderId: orderId)
        default:
            paymentFlowState = .paymentFailed(error: "不支持的支付方式")
        }
    }
    
    // MARK: - Private Methods
    

    
    /// 处理支付响应
    /// - Parameters:
    ///   - response: 支付响应
    ///   - completion: 完成回调
    private func handlePaymentResponse(_ response: PaymentResponse, completion: @escaping (Bool, String) -> Void) {
        switch currentPaymentMethod {
        case PaymentMethodIdentifiers.wallet:
            // 余额支付直接成功
            paymentFlowState = .paymentCompleted(orderId: currentOrderId)
            paymentSuccess = true
            completion(true, "余额支付成功")

        case PaymentMethodIdentifiers.alipay, PaymentMethodIdentifiers.wechat, PaymentMethodIdentifiers.mix:
            // 第三方支付需要调用第三方SDK
            processThirdPartyPayment(orderId: currentOrderId)
            completion(true, "获取支付参数成功")

        default:
            paymentFlowState = .paymentFailed(error: "不支持的支付方式")
            completion(false, "不支持的支付方式")
        }
    }
    
    /// 获取支付方式字符串
    /// - Returns: 支付方式字符串
    private func getPayMethodString() -> String {
        switch currentPaymentMethod {
        case PaymentMethodIdentifiers.wallet:
            return "wallet"
        case PaymentMethodIdentifiers.alipay:
            return "alipay"
        case PaymentMethodIdentifiers.wechat:
            return "wechat"
        case PaymentMethodIdentifiers.mix:
            return "mix"
        default:
            return "alipay" // 默认支付宝
        }
    }
    
    /// 处理支付宝支付
    /// - Parameter orderId: 订单ID
    private func handleAlipayPayment(orderId: Int) {
        guard let result = paymentResult,
              let signedString = result.alipay_signed_string else {
            paymentFlowState = .paymentFailed(error: "支付参数错误")
            return
        }
        
        // 调用支付宝支付
        PaymentManager.shared.startAlipayPayment(signedString: signedString) { [weak self] paymentResult in
            DispatchQueue.main.async {
                self?.handleThirdPartyPaymentResult(paymentResult, orderId: orderId)
            }
        }
    }
    
    /// 处理微信支付
    /// - Parameter orderId: 订单ID
    private func handleWechatPayment(orderId: Int) {
        guard let result = paymentResult,
              let payParams = result.wechat_pay_params else {
            paymentFlowState = .paymentFailed(error: "支付参数错误")
            return
        }
        
        // 调用微信支付
        PaymentManager.shared.startWechatPayment(payParams: payParams) { [weak self] paymentResult in
            DispatchQueue.main.async {
                self?.handleThirdPartyPaymentResult(paymentResult, orderId: orderId)
            }
        }
    }
    
    /// 处理组合支付
    /// - Parameter orderId: 订单ID
    private func handleCombinePayment(orderId: Int) {
        // 组合支付根据第三方支付方式选择对应的处理方法
        switch combineThirdPartyPaymentMethod {
        case "alipay":
            handleAlipayPayment(orderId: orderId)
        case "wechat":
            handleWechatPayment(orderId: orderId)
        default:
            handleAlipayPayment(orderId: orderId) // 默认使用支付宝
        }
    }
    
    /// 处理第三方支付结果
    private func handleThirdPartyPaymentResult(_ result: PaymentResult, orderId: Int) {
        switch result {
        case .success(let tradeNo):
            notifyPaymentSuccess(orderId: orderId, tradeNo: tradeNo) { [weak self] success, message in
                if !success {
                    print("通知服务器支付成功失败: \(message)")
                }
            }

        case .failure(let error):
            paymentFlowState = .paymentFailed(error: error)

        case .cancelled:
            paymentFlowState = .paymentFailed(error: "支付超时")
        }
    }
}

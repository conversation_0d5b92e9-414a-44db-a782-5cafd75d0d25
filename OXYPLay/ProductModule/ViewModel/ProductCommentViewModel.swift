//
//  ProductCommentViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/2.
//

import UIKit
import Combine

/// 商品评价ViewModel
class ProductCommentViewModel: BaseViewModel {
    
    // MARK: - Published Properties
    
    /// 商品ID
    @Published var productId: Int = 0
    
    /// 评分（1-5）
    @Published var rating: Int = 5
    
    /// 评论内容
    @Published var content: String = ""
    
    /// 款式样式
    @Published var specValueText: String = ""
    
    /// 选中的图片数组
    @Published var selectedImages: [UIImage] = []
    
    /// 评价创建状态
    @Published var createCommentState: RequestState = .idle
    
    /// 评价创建成功的响应
    @Published var createCommentResponse: ProductCommentCreateResponse?
    
    // MARK: - Private Properties
    
    /// 当前评价创建任务的取消令牌
    private var currentCreateCancellable: AnyCancellable?
    
    // MARK: - 初始化
    
    /// 初始化
    /// - Parameters:
    ///   - productId: 商品ID
    ///   - specValueText: 款式样式文本
    init(productId: Int, specValueText: String = "") {
        self.productId = productId
        self.specValueText = specValueText
        super.init()
        setupImageUploadBindings()
    }
    
    // MARK: - 设置绑定
    
    /// 设置图片上传相关绑定
    private func setupImageUploadBindings() {
        // 监听图片上传状态变化
        imageUploadManager.$uploadState
            .receive(on: DispatchQueue.main)
            .sink { [weak self] uploadState in
                guard let self = self else { return }
                
                switch uploadState {
                case .success:
                    // 图片上传成功，开始创建评价
                    self.performCreateComment()
                case .failure(let message):
                    // 图片上传失败，更新创建状态
                    self.createCommentState = .failure("图片上传失败: \(message)")
                default:
                    break
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 公共方法
    
    
    /// 更新评论内容
    /// - Parameter newContent: 新的评论内容
    func updateContent(_ newContent: String) {
        content = newContent
    }
    
    
    /// 创建商品评价
    func createComment() {
        // 验证必填字段
        guard productId > 0 else {
            createCommentState = .failure("商品ID无效")
            return
        }
        
        guard rating >= 1 && rating <= 5 else {
            createCommentState = .failure("请选择评分")
            return
        }
        
        // 取消之前的创建任务
        currentCreateCancellable?.cancel()
        
        createCommentState = .loading
        
        // 如果有图片需要上传，先上传图片
        if !selectedImages.isEmpty {
            imageUploadManager.uploadImages(selectedImages)
        } else {
            // 没有图片，直接创建评价
            performCreateComment()
        }
    }
    
    /// 取消创建评价
    func cancelCreateComment() {
        currentCreateCancellable?.cancel()
        imageUploadManager.cancelUpload()
        createCommentState = .idle
    }
    
  
    // MARK: - 私有方法
    
    /// 执行创建评价请求
    private func performCreateComment() {
        // 构建请求参数
        let imageUrls = imageUploadManager.uploadedFileUrls.map { $0.url }
        
        let request = ProductCommentCreateRequest(
            product_id: productId,
            rating: rating,
            content: content.trimmingCharacters(in: .whitespacesAndNewlines),
            images: imageUrls,
            spec_value_text: specValueText
        )
        
        let params = RequestParameters(request.asParameters())
        
        // 发送创建评价请求
        currentCreateCancellable = requestModel(
            HomeService.commenCreate(params: params),
            type: ProductCommentCreateResponse.self
        )
        .receive(on: DispatchQueue.main)
        .sink(
            receiveCompletion: { [weak self] completion in
                guard let self = self else { return }
                
                if case .failure(let error) = completion {
                    self.createCommentState = .failure(self.handleCreateCommentError(error))
                }
            },
            receiveValue: { [weak self] response in
                guard let self = self else { return }
                
                self.createCommentResponse = response
                self.createCommentState = .success
                print("商品评价创建成功，ID: \(response.id)")
            }
        )
    }
    
    /// 处理创建评价错误
    /// - Parameter error: 网络错误
    /// - Returns: 错误信息
    private func handleCreateCommentError(_ error: NetworkError) -> String {
        switch error {
        case .networkError(let response):
            return response.message
        case .decodingError:
            return "评价创建失败，请重试"
        case .noConnection:
            return "网络连接失败，请检查网络设置"
        case .tokenExpired:
            return "登录已过期，请重新登录"
        case .tokenError:
            return "登录状态异常，请重新登录"
        }
    }
    
    // MARK: - 资源释放
    
    deinit {
        currentCreateCancellable?.cancel()
    }
}

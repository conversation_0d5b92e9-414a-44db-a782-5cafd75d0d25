//
//  ProductOrderConfirmation​ViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/18.
//

class ProductOrderConfirmation​ViewModel: BaseViewModel {

    // MARK: - Published Properties

    /// 优惠卷列表数据
    @Published var couponModel: CouponModel?
    @Published var productAddressModel: ProductAddressModel?

    /// 订单创建结果
    @Published var createOrderResult: CreateOrderResponse?



    /// 余额支付金额
    @Published var walletAmount: Float = 0.0

    /// 第三方支付金额
    @Published var thirdPartyAmount: Float = 0.0

    /// 组合支付时的第三方支付方式
    private var combineThirdPartyPaymentMethod = ""
    // MARK: - Initialization

    override init() {
        super.init()
    }

    // MARK: - Public Methods

    /// 获取优惠卷列表
    func fetchCouponList(advertiser_id: String,order_amount: String) {
        var params = RequestParameters(["": ""])
            params = RequestParameters([
                "advertiser_id": advertiser_id,
                "order_amount": order_amount,
            ])
        requestModel(MineService.couponList(params: params), type:CouponModel.self)
            .sink { [weak self] completion in

            } receiveValue: { [weak self] models in
                // 设置默认选择后再赋值
                var mutableModels = models
                let processedModels = mutableModels.setDefaultSelection()
                self?.couponModel = processedModels
            }
            .store(in: &cancellables)
    }
    
    /// 获取位置列表
    func fetchLocationList() {
        var params = RequestParameters(["": ""])
            params = RequestParameters([
                "type": "1",
            ])
        requestModel(MineService.addressList(params: params), type:ProductAddressModel.self)
            .sink { [weak self] completion in
            } receiveValue: { [weak self] models in
                // 只在没有选中状态时设置默认选择
                var mutableModels = models
                let processedModels = mutableModels.setDefaultSelectionIfNeeded()
                self?.productAddressModel = processedModels
            }
            .store(in: &cancellables)
    }
  

    /// 设置默认收货地址
    /// - Parameter addressId: 地址ID
    func setDefaultAddress(addressId: String, completion: @escaping (Bool) -> Void) {
        let params = RequestParameters([
            "address_id": addressId,
            "type": "1"
        ])

        request(MineService.setDefaultAddress(params: params))
            .sink { completionResult in
                switch completionResult {
                case .failure:
                    completion(false)
                case .finished:
                    break
                }
            } receiveValue: { [weak self] response in
                if response.code == 200 {
                    // 设置成功后重新获取地址列表
                    self?.fetchLocationList()
                    completion(true)
                } else {
                    completion(false)
                }
            }
            .store(in: &cancellables)
    }

    /// 创建新地址
    /// - Parameters:
    ///   - addressData: 地址数据字典
    ///   - completion: 完成回调
    func createAddress(addressData: [String: Any], completion: @escaping (Bool) -> Void) {
        let params = RequestParameters(addressData)

        requestModel(MineService.createAddress(params: params), type: CreateAddressResponse.self)
            .sink { completionResult in
                switch completionResult {
                case .failure:
                    completion(false)
                case .finished:
                    break
                }
            } receiveValue: { [weak self] response in
                // 创建成功后重新获取地址列表
                self?.fetchLocationList()
                completion(true)
            }
            .store(in: &cancellables)
    }

    /// 更新地址
    /// - Parameters:
    ///   - addressData: 地址数据字典
    ///   - completion: 完成回调
    func updateAddress(addressData: [String: Any], completion: @escaping (Bool) -> Void) {
        let params = RequestParameters(addressData)

        request(MineService.updateAddress(params: params))
            .sink { completionResult in
                switch completionResult {
                case .failure:
                    completion(false)
                case .finished:
                    break
                }
            } receiveValue: { [weak self] response in
                if response.code == 200 {
                    // 更新成功后重新获取地址列表
                    self?.fetchLocationList()
                    completion(true)
                } else {
                    completion(false)
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - 订单创建方法

    /// 创建服务订单
    /// - Parameters:
    ///   - serviceModel: 服务帖详情模型
    ///   - completion: 完成回调
    func createServiceOrder(serviceModel: HomeDetailModel, completion: @escaping (Bool, String) -> Void) {
        // 验证必要参数
        guard let selectedAddress = productAddressModel?.selectedAddress() else {
            completion(false, "请选择收货地址")
            return
        }

        // 计算支付金额
        let servicePrice = Float(serviceModel.price) ?? 0.0
        let discountAmount = Float(couponModel?.getCurrentSelectedPrice() ?? "0") ?? 0.0
        let finalAmount = servicePrice - discountAmount

        // 根据设置的支付金额确定支付方式
        let walletPayAmount = walletAmount
        let thirdPartyPayAmount = thirdPartyAmount

        // 验证支付金额
        if abs((walletPayAmount + thirdPartyPayAmount) - finalAmount) > 0.01 {
            completion(false, "支付金额不一致")
            return
        }

        // 构建请求参数
        var params: [String: Any] = [
            "service_id": serviceModel.id,
            "service_type": serviceModel.type,
            "quantity": 1,
            "address_id": selectedAddress.id,
            "wallet_amount": walletPayAmount,
            "third_party_amount": thirdPartyPayAmount,
            "payment_type": combineThirdPartyPaymentMethod.isEmpty ? "alipay" : combineThirdPartyPaymentMethod
        ]

        // 添加优惠券参数
        if let selectedCoupon = couponModel?.getCurrentSelectedCoupon() {
            params["coupon_id"] = selectedCoupon.id
            params["discount_amount"] = discountAmount
        }

        let requestParams = RequestParameters(params)

        requestModel(OrderService.createService(params: requestParams), type: CreateOrderResponse.self)
            .sink { completionResult in
                switch completionResult {
                case .failure(let error):
                    completion(false, error.localizedDescription)
                case .finished:
                    break
                }
            } receiveValue: { [weak self] response in
                self?.createOrderResult = response
                completion(true, "服务下单成功")
            }
            .store(in: &cancellables)
    }

    /// 立即购买商品
    /// - Parameters:
    ///   - productModel: 商品详情模型
    ///   - completion: 完成回调
    func buyNowProduct(productModel: ProductDetailModel, completion: @escaping (Bool, String) -> Void) {
        // 验证必要参数
        guard let selectedAddress = productAddressModel?.selectedAddress() else {
            completion(false, "请选择收货地址")
            return
        }

        // 获取当前选中的SKU信息
        guard let selectedSku = productModel.getSelectedSku() else {
            completion(false, "请选择商品规格")
            return
        }

        // 计算支付金额
        let productPrice = Float(productModel.getcurrentQuantityPrice()) ?? 0.0
        let discountAmount = Float(couponModel?.getCurrentSelectedPrice() ?? "0") ?? 0.0
        let finalAmount = productPrice - discountAmount

        // 根据设置的支付金额确定支付方式
        let walletPayAmount = walletAmount
        let thirdPartyPayAmount = thirdPartyAmount

        // 验证支付金额
        if abs((walletPayAmount + thirdPartyPayAmount) - finalAmount) > 0.01 {
            completion(false, "支付金额不一致")
            return
        }

        // 构建请求参数
        var params: [String: Any] = [
            "product_id": productModel.product.id,
            "sku_id": selectedSku.sku_id,
            "quantity": productModel.currentQuantity,
            "address_id": selectedAddress.id,
            "wallet_amount": walletPayAmount,
            "third_party_amount": thirdPartyPayAmount,
            "payment_type": combineThirdPartyPaymentMethod.isEmpty ? "alipay" : combineThirdPartyPaymentMethod
        ]

        // 添加优惠券参数
        if let selectedCoupon = couponModel?.getCurrentSelectedCoupon() {
            params["coupon_id"] = selectedCoupon.id
            params["discount_amount"] = discountAmount
        }

        let requestParams = RequestParameters(params)

        requestModel(OrderService.buyNow(params: requestParams), type: CreateOrderResponse.self)
            .sink { completionResult in
                switch completionResult {
                case .failure(let error):
                    completion(false, error.localizedDescription)
                case .finished:
                    break
                }
            } receiveValue: { [weak self] response in
                self?.createOrderResult = response
                completion(true, "下单成功")
            }
            .store(in: &cancellables)
    }

    // MARK: - 辅助方法






    /// 更新组合支付方式三方选择
    /// - Parameter paymentMethod: 支付方式标识
    func updateCombineThirdPartyPaymentMethod(_ paymentMethod: String) {
        combineThirdPartyPaymentMethod = paymentMethod
    }
    /// 更新支付金额
    /// - Parameters:
    ///   - walletAmount: 余额支付金额
    ///   - thirdPartyAmount: 第三方支付金额
    func updatePaymentAmounts(walletAmount: Float, thirdPartyAmount: Float) {
        self.walletAmount = walletAmount
        self.thirdPartyAmount = thirdPartyAmount
    }


}











//
//  ProductSellingPurchasedViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/19.
//

import Foundation
import Combine

/// 订单类型枚举
enum OrderViewType {
    case selling  // 我卖出的
    case purchased // 我买到的
}

/// 订单状态筛选枚举
enum OrderStatusFilter: Int, CaseIterable {
    case all = 0        // 全部
    case pending = 1    // 待付款
    case shipping = 2   // 待发货
    case receiving = 3  // 待收货
    case reviewing = 4  // 评价
    case refunding = 5  // 退款

    var title: String {
        switch self {
        case .all: return "全部"
        case .pending: return "待付款"
        case .shipping: return "待发货"
        case .receiving: return "待收货"
        case .reviewing: return "评价"
        case .refunding: return "退款"
        }
    }
}

class ProductSellingPurchasedViewModel: BaseViewModel {

    // MARK: - Published Properties

    /// 订单列表数据（每个元素代表一个section）
    @Published var orders: [ProductOrderModel] = []

    /// 订单类型（卖出/买到）
    var orderType: OrderViewType = .selling

    /// 当前状态筛选
    var statusFilter: OrderStatusFilter = .all

    // MARK: - Initialization

    init(orderType: OrderViewType) {
        self.orderType = orderType
        super.init()
    }

    override init() {
        super.init()
    }

    // MARK: - 绑定

    override func setupBindings() {
        super.setupBindings()
    }

    // MARK: - Public Methods

    /// 刷新数据
    override func refreshData() {
        fetchOrderList(isRefresh: true)
    }

    /// 加载更多数据
    override func loadMoreData() {
        fetchOrderList(isRefresh: false)
    }

    /// 设置状态筛选并刷新数据
    func setStatusFilter(_ filter: OrderStatusFilter) {
        statusFilter = filter
        refreshData()
    }

    /// 获取订单列表（统一的请求方法）
    func fetchOrderList(isRefresh: Bool = true) {
        if isRefresh {
            refreshState = .headerRefreshing
            currentPage = 1
            hasMoreData = true
        } else {
            guard hasMoreData else {
                refreshState = .noMoreData
                return
            }
            refreshState = .footerLoading
            currentPage += 1
        }

        // 构建请求参数
        let params = RequestParameters([
            "page": currentPage,
            "page_size": pageSize,
            "status": statusFilter == .all ? nil : statusFilter.rawValue
        ].compactMapValues { $0 })

        // 根据订单类型选择对应的服务
        let service: MineService = orderType == .selling ? .soldOrders(params: params) : .boughtOrders(params: params)

        // 使用便捷的分页数据请求方法
        requestPageData(service, type: ProductOrderModel.self, isRefresh: isRefresh) { [weak self] newOrders, isRefresh in
            guard let self = self else { return }

            // 更新订单数据
            if isRefresh {
                self.orders = newOrders
            } else {
                self.orders.append(contentsOf: newOrders)
            }
        }
        .sink(
            receiveCompletion: { completion in
                if case .failure(let error) = completion {
                    print("获取订单列表失败: \(error.localizedDescription)")
                }
            },
            receiveValue: { _ in
                print("订单列表加载成功")
            }
        )
        .store(in: &cancellables)
    }

    ///取消订单
    func cancelOrder(orderId:Int){
        let params = RequestParameters([
            "order_id": orderId
        ])
        requestModel(OrderService.cancel(params: params), type: ResponseModel.self)
            .sink { [weak self] completion in
                if case .failure(let error) = completion {
                    print("加载订单详情失败: \(error.localizedDescription)")
                }
            } receiveValue: { [weak self] response in
                self?.refreshData()
            }
            .store(in: &cancellables)
    }

}

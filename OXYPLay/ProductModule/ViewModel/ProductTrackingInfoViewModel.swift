//
//  ProductTrackingInfoViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/3.
//

import Foundation
import Combine

/// 物流跟踪信息ViewModel
class ProductTrackingInfoViewModel: BaseViewModel {
    
    // MARK: - Published Properties
    
    /// 物流轨迹数据
    @Published var trackItems: [LogisticsTrackItem] = []
    
    /// 是否正在加载（使用父类的isLoading属性）
    
    /// 错误信息
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    
    /// 物流信息
    private var logistics: OrderLogistics?
    
    // MARK: - Public Methods
    
    /// 设置物流信息
    /// - Parameter logistics: 订单物流信息
    func setLogistics(_ logistics: OrderLogistics) {
        self.logistics = logistics
    }
    
    /// 查询物流轨迹信息
    func fetchLogisticsInfo() {
        guard let logistics = logistics,
              !logistics.express_no.isEmpty,
              !logistics.express_company.isEmpty else {
            return
        }

        isLoading = true
        errorMessage = nil

        let params = LogisticsQueryRequest(
            kuaidicom: logistics.express_company,
            kuaidinum: logistics.express_no,
            phone: nil
        )

        requestModel(LogisticsService.queryLogistics(params: params), type: LogisticsQueryResponse.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    guard let self = self else { return }
                    self.isLoading = false

                    if case let .failure(error) = completion {
                        self.handleError(error)
                      
                    }
                },
                receiveValue: { [weak self] response in
                    guard let self = self else { return }
                    self.trackItems = response.data
                    print("物流轨迹查询成功，共\(response.data.count)条记录")
                }
            )
            .store(in: &cancellables)
    }

  
    
    // MARK: - Private Methods
    
    /// 处理网络错误
    /// - Parameter error: 网络错误
    private func handleError(_ error: NetworkError) {
        switch error {
        case .networkError(let response):
            errorMessage = response.message.isEmpty ? "查询物流信息失败" : response.message
        case .decodingError(let message):
            errorMessage = "数据解析错误"
            print("物流数据解析错误: \(message)")
        case .noConnection:
            errorMessage = "网络连接失败，请检查网络设置"
        case .tokenExpired:
            errorMessage = "登录已过期，请重新登录"
        case .tokenError:
            errorMessage = "登录状态异常，请重新登录"
        }
    }
}

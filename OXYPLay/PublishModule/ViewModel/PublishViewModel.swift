//
//  PublishViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/15.
//

import UIKit
import Combine

class PublishViewModel: BaseViewModel {

    // MARK: - Published属性

    /// 发布状态
    @Published var publishState: RequestState = .idle

    /// 发布成功的帖子ID
    @Published var publishedPostId: String = ""

    /// 草稿保存状态
    @Published var draftSaveState: RequestState = .idle

    /// 草稿检查状态
    @Published var draftCheckState: RequestState = .idle

    /// 现有草稿信息
    @Published var existingDraft: CheckDraftResponse?

    // MARK: - 私有属性

    /// 当前发布任务的取消令牌
    private var currentPublishCancellable: AnyCancellable?

    // MARK: - 便捷属性（代理到imageUploadManager）

    /// 已上传的文件URL数组
    var uploadedFileUrls: [UploadFileResponse] {
        return imageUploadManager.uploadedFileUrls
    }

    /// 上传状态 - 使用Published包装器
    @Published var uploadState: RequestState = .idle

    // MARK: - 初始化

    override init() {
        super.init()
        setupImageUploadBindings()
    }

    /// 设置图片上传状态绑定
    private func setupImageUploadBindings() {
        // 同步图片上传管理器的状态到本地Published属性
        imageUploadManager.$uploadState
            .assign(to: &$uploadState)
    }

}
extension PublishViewModel{
    
    // MARK: - 公共方法

    /// 上传图片列表（使用flatMap确保顺序，任一失败则整体失败）
    /// - Parameter images: 要上传的图片数组
    func uploadImages(_ images: [UIImage]) {
        imageUploadManager.uploadImages(images)
    }

    /// 上传视频文件
    /// - Parameter videoURL: 视频文件URL
    func uploadVideo(_ videoURL: URL) {
        imageUploadManager.uploadVideo(videoURL)
    }

    // MARK: - 上传相关方法

    /// 取消当前上传任务
    func cancelUpload() {
        imageUploadManager.cancelUpload()
    }

    /// 检查是否有上传任务正在进行
    /// - Returns: 是否正在上传
    func isUploading() -> Bool {
        return imageUploadManager.isUploading()
    }

    /// 发布服务帖子
    /// - Parameters:
    ///   - request: 发布请求参数
    ///   - selectedUsers: 选中的用户列表（用于黑白名单）
    func publishServicePost(_ request: CreateServicePostRequest, selectedUsers: [MineAddFriendItemModel] = []) {
        // 取消之前的发布任务
        currentPublishCancellable?.cancel()

        publishState = .loading

        // 创建帖子
        currentPublishCancellable = createServicePost(request)
            .flatMap { [weak self] response -> AnyPublisher<CreateServicePostResponse, NetworkError> in
                guard let self = self else {
                    return Fail(error: NetworkError.decodingError("ViewModel已释放"))
                        .eraseToAnyPublisher()
                }

                self.publishedPostId = response.id

                // 如果需要设置可见性，继续调用相关接口
                if request.visibility == 4 || request.visibility == 5, !selectedUsers.isEmpty {
                    let userIds = selectedUsers.map { $0.id }
                    return self.setPostVisibility(
                        postId: response.id,
                        type: request.type,
                        visibility: request.visibility,
                        userIds: userIds
                    )
                    .map { _ in response } // 返回原始响应
                    .eraseToAnyPublisher()
                } else {
                    return Just(response)
                        .setFailureType(to: NetworkError.self)
                        .eraseToAnyPublisher()
                }
            }
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    guard let self = self else { return }

                    if case .failure(let error) = completion {
                        self.publishState = .failure(self.handlePublishError(error))
                    }
                },
                receiveValue: { [weak self] response in
                    guard let self = self else { return }

                    self.publishedPostId = response.id
                    self.publishState = .success
                }
            )
    }

    // MARK: - 私有发布方法

    /// 创建服务帖子
    private func createServicePost(_ request: CreateServicePostRequest) -> AnyPublisher<CreateServicePostResponse, NetworkError> {
        let params = RequestParameters(request.toDictionary() ?? [:])
        return requestModel(AssemblyService.create(params: params), type: CreateServicePostResponse.self)
    }

    /// 设置帖子可见性
    private func setPostVisibility(postId: String, type: Int, visibility: Int, userIds: [String]) -> AnyPublisher<ResponseModel, NetworkError> {
        if visibility == 4 { // 不给谁看
            let visibilityRequest = VisibilityRequest(
                type: type,
                post_id: postId,
                block_user_ids: userIds
            )
            let params = RequestParameters(visibilityRequest.toDictionary() ?? [:])
            return request(AssemblyService.visibilityBlock(params: params))
        } else if visibility == 5 { // 只给谁看
            let visibilityRequest = VisibilityRequest(
                type: type,
                post_id: postId,
                allow_user_ids: userIds
            )
            let params = RequestParameters(visibilityRequest.toDictionary() ?? [:])
            return request(AssemblyService.visibilityWhitelist(params: params))
        } else {
            return Just(ResponseModel(code: 200, message: "无需设置可见性"))
                .setFailureType(to: NetworkError.self)
                .eraseToAnyPublisher()
        }
    }

    /// 检查是否存在草稿
    func checkExistingDraft() {
        draftCheckState = .loading

        let params = RequestParameters([:])

        requestModel(AssemblyService.check(params: params), type: CheckDraftResponse.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    guard let self = self else { return }

                    if case .failure(let error) = completion {
                        self.draftCheckState = .failure(self.handlePublishError(error))
                        self.existingDraft = nil
                    } else {
                        self.draftCheckState = .success
                    }
                },
                receiveValue: { [weak self] draft in
                    guard let self = self else { return }
                    self.existingDraft = draft
                }
            )
            .store(in: &cancellables)
    }

    /// 保存草稿
    /// - Parameter request: 草稿请求参数
    func saveDraft(_ request: CreateServicePostRequest) {
        draftSaveState = .loading

        let params = RequestParameters(request.toDictionary() ?? [:])

        requestModel(AssemblyService.saveDraft(params: params), type: SaveDraftResponse.self)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    guard let self = self else { return }

                    if case .failure(let error) = completion {
                        self.draftSaveState = .failure(self.handlePublishError(error))
                    } else {
                        self.draftSaveState = .success
                    }
                },
                receiveValue: { [weak self] response in
                    guard let self = self else { return }
                    print("草稿保存成功，ID: \(response.id)")
                }
            )
            .store(in: &cancellables)
    }

    // MARK: - 错误处理

    /// 处理发布相关错误
    /// - Parameter error: 网络错误
    /// - Returns: 错误信息
    private func handlePublishError(_ error: NetworkError) -> String {
        switch error {
        case .networkError(let response):
            return response.message
        case .decodingError:
            return "数据处理失败，请重试"
        case .noConnection:
            return "网络连接失败，请检查网络设置"
        case .tokenExpired:
            return "登录已过期，请重新登录"
        case .tokenError:
            return "登录状态异常，请重新登录"
        }
    }
}

//
//  WalletBillDetailViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import Foundation
import Combine
import SwiftDate

/// 账单明细ViewModel
class WalletBillDetailViewModel: BaseViewModel {
    
    // MARK: - Published Properties
    
    /// 账单数据按月份分组
    @Published var monthlyBillData: [MonthlyBillSection] = []
    
    /// 当前选中的账单类型
    @Published var selectedBillType: String? = nil
    
    /// 账单类型显示文本
    @Published var billTypeDisplayText: String = "全部账单"
    
    // MARK: - Private Properties

    /// 已加载的月份数据缓存
    private var monthDataCache: [String: WalletTransactionListModel] = [:]

    /// 当前加载的月份列表（按时间倒序排列，最新的在前面）
    private var loadedMonths: [String] = []

    /// 基准月份（用户选择的起始月份或当前月份）
    private var baseMonth: String = ""

    /// 是否使用假数据进行测试
    private let useTestData = false
    
    // MARK: - 初始化
    
    override init() {
        super.init()
        setupInitialData()
    }
    
    // MARK: - 数据加载
    
    /// 初始化数据
    private func setupInitialData() {
        // 使用SwiftDate设置基准月份为当前月份
        baseMonth = Date().toFormat("yyyy-MM")

        // 初始只加载当前月份
        loadedMonths = [baseMonth]

        // 加载当前月份数据
        fetchBillData(for: baseMonth, isRefresh: true)
    }
    
    /// 获取指定月份的账单数据
    /// - Parameters:
    ///   - month: 月份，格式为 YYYY-MM
    ///   - isRefresh: 是否为刷新操作
    func fetchBillData(for month: String, isRefresh: Bool = false) {
        // 如果缓存中已有数据且不是刷新操作，直接使用缓存
        if !isRefresh, let cachedData = monthDataCache[month] {
            updateMonthlyData(with: cachedData, for: month)
            return
        }

        if useTestData {
            // 使用假数据进行测试
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                let testData = self?.generateTestData(for: month)
                if let data = testData {
                    self?.monthDataCache[month] = data
                    self?.updateMonthlyData(with: data, for: month)
                }
            }
        } else {
            // 真实网络请求
            let request = WalletTransactionListRequest(month: month, type: selectedBillType)

            requestModel(WalletService.transactionList(params: request), type: WalletTransactionListModel.self)
                .receive(on: DispatchQueue.main)
                .sink(
                    receiveCompletion: { [weak self] completion in
                        if case let .failure(error) = completion {
                            self?.handleError(error)
                        }
                    },
                    receiveValue: { [weak self] billData in
                        self?.monthDataCache[month] = billData
                        self?.updateMonthlyData(with: billData, for: month)
                    }
                )
                .store(in: &cancellables)
        }
    }
    
    /// 刷新数据（重新加载基准月份）
    func refreshAllData() {
        refreshState = .headerRefreshing

        // 清空所有数据，重新从基准月份开始
        monthDataCache.removeAll()
        monthlyBillData.removeAll()
        loadedMonths = [baseMonth]

        // 重新加载基准月份数据
        if useTestData {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                let testData = self?.generateTestData(for: self?.baseMonth ?? "")
                if let data = testData, let month = self?.baseMonth {
                    self?.monthDataCache[month] = data
                    self?.updateMonthlyData(with: data, for: month)
                    self?.refreshState = .refreshSuccess
                }
            }
        } else {
            let request = WalletTransactionListRequest(month: baseMonth, type: selectedBillType)

            requestModel(WalletService.transactionList(params: request), type: WalletTransactionListModel.self)
                .receive(on: DispatchQueue.main)
                .sink(
                    receiveCompletion: { [weak self] completion in
                        if case let .failure(error) = completion {
                            self?.handleError(error)
                            self?.refreshState = .refreshFailure("刷新失败")
                        } else {
                            self?.refreshState = .refreshSuccess
                        }
                    },
                    receiveValue: { [weak self] billData in
                        self?.monthDataCache[self?.baseMonth ?? ""] = billData
                        self?.updateMonthlyData(with: billData, for: self?.baseMonth ?? "")
                    }
                )
                .store(in: &cancellables)
        }
    }
    
    /// 更新账单类型筛选
    /// - Parameter type: 账单类型
    func updateBillType(_ type: String?) {
        // 如果类型没有变化，不需要重新加载
        if selectedBillType == type {
            return
        }

        selectedBillType = type

        // 更新显示文本
        switch type {
        case "recharge":
            billTypeDisplayText = "充值"
        case "income":
            billTypeDisplayText = "收入"
        case "refund":
            billTypeDisplayText = "退款"
        case "withdraw":
            billTypeDisplayText = "提现"
        default:
            billTypeDisplayText = "全部账单"
        }

        // 清空缓存，重新加载数据
        monthDataCache.removeAll()
        monthlyBillData.removeAll()
        loadedMonths = [baseMonth]

        // 重新加载基准月份数据
        fetchBillData(for: baseMonth, isRefresh: true)
    }

    /// 跳转到指定月份
    /// - Parameter month: 目标月份，格式为 YYYY-MM
    func jumpToMonth(_ month: String) {
        // 更新基准月份
        baseMonth = month

        // 清空当前数据
        monthlyBillData.removeAll()
        monthDataCache.removeAll()
        loadedMonths = [month]

        // 加载目标月份数据
        fetchBillData(for: month, isRefresh: true)
    }
    
    // MARK: - 重写BaseViewModel方法
    
    override func refreshData() {
        refreshAllData()
    }
    
    override func loadMoreData() {
        // 检查是否还有更多数据可以加载
        guard hasMoreData else {
            refreshState = .noMoreData
            return
        }

        refreshState = .footerLoading

        // 获取当前最早的月份（列表最后一个）
        guard let lastMonth = loadedMonths.last else {
            refreshState = .noMoreData
            return
        }

        // 生成上一个月的数据
        guard let prevMonth = generatePreviousMonth(from: lastMonth) else {
            hasMoreData = false
            refreshState = .noMoreData
            return
        }

        // 检查是否已经加载过这个月份
        if loadedMonths.contains(prevMonth) {
            refreshState = .loadMoreSuccess
            return
        }

        loadedMonths.append(prevMonth)

        // 加载上一个月的数据
        if useTestData {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                let testData = self?.generateTestData(for: prevMonth)
                if let data = testData {
                    self?.monthDataCache[prevMonth] = data
                    self?.updateMonthlyData(with: data, for: prevMonth)
                    self?.refreshState = .loadMoreSuccess
                } else {
                    self?.refreshState = .loadMoreFailure("加载失败")
                }
            }
        } else {
            let request = WalletTransactionListRequest(month: prevMonth, type: selectedBillType)

            requestModel(WalletService.transactionList(params: request), type: WalletTransactionListModel.self)
                .receive(on: DispatchQueue.main)
                .sink(
                    receiveCompletion: { [weak self] completion in
                        if case let .failure(error) = completion {
                            self?.handleError(error)
                            self?.refreshState = .loadMoreFailure("加载失败")
                        } else {
                            self?.refreshState = .loadMoreSuccess
                        }
                    },
                    receiveValue: { [weak self] billData in
                        self?.monthDataCache[prevMonth] = billData
                        self?.updateMonthlyData(with: billData, for: prevMonth)
                    }
                )
                .store(in: &cancellables)
        }
    }
    
    // MARK: - Private Methods
    
    /// 更新指定月份的数据
    private func updateMonthlyData(with billData: WalletTransactionListModel, for month: String) {
        let section = MonthlyBillSection(
            month: month,
            displayMonth: formatMonthDisplay(month),
            incomeTotal: billData.income_total,
            expenseTotal: billData.expense_total,
            records: billData.records
        )

        // 更新或添加月份数据
        if let index = monthlyBillData.firstIndex(where: { $0.month == month }) {
            monthlyBillData[index] = section
        } else {
            // 按月份排序插入
            let insertIndex = monthlyBillData.firstIndex { $0.month < month } ?? monthlyBillData.count
            monthlyBillData.insert(section, at: insertIndex)
        }
    }
    
    /// 重新构建所有月份数据
    private func rebuildMonthlyData() {
        monthlyBillData.removeAll()
        
        for month in loadedMonths.sorted(by: >) {
            if let billData = monthDataCache[month] {
                updateMonthlyData(with: billData, for: month)
            }
        }
    }
    
    /// 生成最近几个月的月份列表
    private func generateRecentMonths(count: Int, startFrom: Int = 0) -> [String] {
        let now = Date()
        var months: [String] = []

        // 限制最多生成24个月的数据（2年）
        let maxMonths = 24
        let actualCount = min(count, maxMonths - startFrom)

        guard actualCount > 0 else { return months }

        for i in startFrom..<(startFrom + actualCount) {
            let date = now - i.months
            months.append(date.toFormat("yyyy-MM"))
        }

        return months
    }

    /// 从目标月份开始生成月份列表
    private func generateMonthsFromTarget(_ targetMonth: String, count: Int) -> [String] {
        guard let targetDate = targetMonth.toDate("yyyy-MM")?.date else {
            return generateRecentMonths(count: count)
        }

        var months: [String] = []

        // 生成目标月份及其之前的月份
        for i in 0..<count {
            let date = targetDate - i.months
            months.append(date.toFormat("yyyy-MM"))
        }

        return months
    }

    /// 生成指定月份的上一个月
    private func generatePreviousMonth(from currentMonth: String) -> String? {
        guard let currentDate = currentMonth.toDate("yyyy-MM")?.date else {
            return nil
        }

        let now = Date()

        // 检查是否已经到达24个月的限制
        let monthsAgo = now.getInterval(toDate: currentDate, component: .month)
        if monthsAgo >= 24 {
            return nil
        }

        // 生成上一个月
        let previousDate = currentDate - 1.months
        return previousDate.toFormat("yyyy-MM")
    }
    
    /// 格式化月份显示
    private func formatMonthDisplay(_ month: String) -> String {
        guard let date = month.toDate("yyyy-MM")?.date else {
            return month
        }
        return date.toFormat("yyyy年MM月")
    }
    
    /// 生成测试数据
    private func generateTestData(for month: String) -> WalletTransactionListModel {
        guard let monthDate = month.toDate("yyyy-MM")?.date else {
            return WalletTransactionListModel()
        }

        let year = monthDate.year
        let monthNum = monthDate.month

        // 生成测试交易记录
        var records: [TransactionRecord] = []
        var incomeTotal: Float = 0
        var expenseTotal: Float = 0

        // 辅助方法：生成该月份的随机日期时间
        func generateRandomDateTime() -> String {
            let randomDay = Int.random(in: 1...28)
            let randomHour = Int.random(in: 9...18)
            let randomMinute = Int.random(in: 0...59)

            let randomDate = DateInRegion(year: year, month: monthNum, day: randomDay, hour: randomHour, minute: randomMinute, second: 0)
            return randomDate.toFormat("yyyy.MM.dd HH:mm:ss")
        }

        // 根据选择的账单类型生成不同的数据
        switch selectedBillType {
        case "recharge":
            // 只生成充值记录
            for _ in 1...Int.random(in: 2...5) {
                var record = TransactionRecord()
                record.remark = "账户充值"
                record.display_date = generateRandomDateTime()
                let amount = Float.random(in: 100...1000)
                record.display_amount = "+\(String(format: "%.2f", amount))"
                record.typeText = "充值"
                incomeTotal += amount
                records.append(record)
            }

        case "income":
            // 只生成收入记录
            let incomeTypes = ["卖出商品", "佣金收入", "推广奖励", "返现收入"]
            for _ in 1...Int.random(in: 3...8) {
                var record = TransactionRecord()
                record.remark = incomeTypes.randomElement() ?? "收入"
                record.display_date = generateRandomDateTime()
                let amount = Float.random(in: 50...500)
                record.display_amount = "+\(String(format: "%.2f", amount))"
                record.typeText = "收入"
                incomeTotal += amount
                records.append(record)
            }

        case "refund":
            // 只生成退款记录
            for _ in 1...Int.random(in: 1...4) {
                var record = TransactionRecord()
                record.remark = "订单退款"
                record.display_date = generateRandomDateTime()
                let amount = Float.random(in: 20...300)
                record.display_amount = "-\(String(format: "%.2f", amount))"
                record.typeText = "退款"
                expenseTotal += amount
                records.append(record)
            }

        case "withdraw":
            // 只生成提现记录
            for _ in 1...Int.random(in: 1...3) {
                var record = TransactionRecord()
                record.remark = "余额提现"
                record.display_date = generateRandomDateTime()
                let amount = Float.random(in: 100...800)
                record.display_amount = "-\(String(format: "%.2f", amount))"
                record.typeText = "提现"
                expenseTotal += amount
                records.append(record)
            }

        default:
            // 全部账单 - 生成所有类型的记录
            // 充值记录
            for _ in 1...Int.random(in: 1...2) {
                var record = TransactionRecord()
                record.remark = "账户充值"
                record.display_date = generateRandomDateTime()
                let amount = Float.random(in: 100...1000)
                record.display_amount = "+\(String(format: "%.2f", amount))"
                record.typeText = "充值"
                incomeTotal += amount
                records.append(record)
            }

            // 收入记录
            let incomeTypes = ["卖出商品", "佣金收入", "推广奖励", "返现收入"]
            for _ in 1...Int.random(in: 3...6) {
                var record = TransactionRecord()
                record.remark = incomeTypes.randomElement() ?? "收入"
                record.display_date = generateRandomDateTime()
                let amount = Float.random(in: 50...500)
                record.display_amount = "+\(String(format: "%.2f", amount))"
                record.typeText = "收入"
                incomeTotal += amount
                records.append(record)
            }

            // 退款记录
            for _ in 1...Int.random(in: 1...2) {
                var record = TransactionRecord()
                record.remark = "订单退款"
                record.display_date = generateRandomDateTime()
                let amount = Float.random(in: 20...300)
                record.display_amount = "-\(String(format: "%.2f", amount))"
                record.typeText = "退款"
                expenseTotal += amount
                records.append(record)
            }

            // 提现记录
            for _ in 1...Int.random(in: 1...2) {
                var record = TransactionRecord()
                record.remark = "余额提现"
                record.display_date = generateRandomDateTime()
                let amount = Float.random(in: 100...800)
                record.display_amount = "-\(String(format: "%.2f", amount))"
                record.typeText = "提现"
                expenseTotal += amount
                records.append(record)
            }
        }

        // 按日期排序（最新的在前面）
        records.sort { $0.display_date > $1.display_date }

        var testData = WalletTransactionListModel()
        testData.month = month
        testData.income_total = String(format: "%.2f", incomeTotal)
        testData.expense_total = String(format: "%.2f", expenseTotal)
        testData.records = records

        return testData
    }

    /// 处理网络错误
    override func handleError(_ error: NetworkError, operation: String = "请求") {
        // 调用父类方法处理空数据状态
        super.handleError(error, operation: operation)

        // 设置刷新状态
        refreshState = .refreshFailure("加载失败")

        // 添加特定的错误处理逻辑
        switch error {
        case .networkError(let response):
            print("网络错误: \(response.message)")
        case .decodingError(let message):
            print("数据解析错误: \(message)")
        case .noConnection:
            print("网络连接失败")
        case .tokenExpired:
            print("Token已过期")
        case .tokenError:
            print("Token错误")
        }
    }
}

// MARK: - 数据模型

/// 月度账单分组数据
class MonthlyBillSection {
    let month: String           // 原始月份 YYYY-MM
    let displayMonth: String    // 显示月份 YYYY年MM月
    let incomeTotal: String     // 月收入总额
    let expenseTotal: String    // 月支出总额
    let records: [TransactionRecord] // 交易记录

    init(month: String, displayMonth: String, incomeTotal: String, expenseTotal: String, records: [TransactionRecord]) {
        self.month = month
        self.displayMonth = displayMonth
        self.incomeTotal = incomeTotal
        self.expenseTotal = expenseTotal
        self.records = records
    }
}

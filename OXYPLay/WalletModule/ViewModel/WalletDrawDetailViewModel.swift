//
//  WalletDrawDetailViewModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/26.
//

import Foundation
import Combine

/// 提现明细ViewModel
class WalletDrawDetailViewModel: BaseViewModel {
    
    // MARK: - Published Properties
    
    /// 提现明细数据按月份分组
    @Published var monthlyWithdrawData: [WithdrawMonthGroup] = []
    
    // MARK: - Private Properties
    
    /// 当前查询的月份
    private var currentMonth: String?
    
    /// 是否使用假数据进行测试
    private let useTestData = false
    
    // MARK: - 初始化
    
    override init() {
        super.init()
        setupInitialData()
    }
    
    // MARK: - 数据加载
    
    /// 初始化数据
    private func setupInitialData() {
        // 加载提现明细数据
        fetchWithdrawData(refresh: true)
    }
    
    /// 获取提现明细数据
    /// - Parameter refresh: 是否为刷新操作
    func fetchWithdrawData(refresh: Bool = false) {
        if refresh {
            refreshState = .headerRefreshing
            currentPage = 1
            hasMoreData = true
        } else {
            guard hasMoreData else {
                refreshState = .noMoreData
                return
            }
            refreshState = .footerLoading
            currentPage += 1
        }
        
        if useTestData {
            // 使用测试数据
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
                self?.handleTestData(isRefresh: refresh)
            }
        } else {
            // 真实网络请求
            let request: WalletWithdrawListRequest
            if let month = currentMonth {
                request = WalletWithdrawListRequest.forMonth(month, page: currentPage, limit: pageSize)
            } else {
                request = WalletWithdrawListRequest.forPage(currentPage, limit: pageSize)
            }
            
            requestModel(WalletService.withdrawList(params: request), type: WalletWithdrawListModel.self)
                .receive(on: DispatchQueue.main)
                .sink(
                    receiveCompletion: { [weak self] completion in
                        if case let .failure(error) = completion {
                            self?.handleError(error)
                        }
                    },
                    receiveValue: { [weak self] withdrawData in
                        self?.handleWithdrawData(withdrawData, isRefresh: refresh)
                    }
                )
                .store(in: &cancellables)
        }
    }
    
    /// 处理提现数据
    private func handleWithdrawData(_ data: WalletWithdrawListModel, isRefresh: Bool) {
        if isRefresh {
            monthlyWithdrawData = data.list
        } else {
            monthlyWithdrawData.append(contentsOf: data.list)
        }
        
        // 更新分页状态
        hasMoreData = data.list.count >= pageSize
        refreshState = isRefresh ? .refreshSuccess : .loadMoreSuccess
    }
    
    /// 处理测试数据
    private func handleTestData(isRefresh: Bool) {
        let testData = createTestData()
        
        if isRefresh {
            monthlyWithdrawData = testData
        } else {
            // 模拟没有更多数据
            hasMoreData = false
            refreshState = .noMoreData
            return
        }
        
        refreshState = .refreshSuccess
    }
    
    /// 创建测试数据
    private func createTestData() -> [WithdrawMonthGroup] {
        return [
            WithdrawMonthGroup(
                month: "2024年7月",
                total_count: 6,
                items: [
                    WithdrawRecord(
                        id: 123456,
                        amount: "1000.00",
                        status: 2,
                        status_text: "待审核",
                        status_color: "#faad14",
                        time_label: "申请时间",
                        time: "2024.01.04 12:00:00",
                        audit_remark: ""
                    ),
                    WithdrawRecord(
                        id: 123456,
                        amount: "1000.00",
                        status: 1,
                        status_text: "已打款",
                        status_color: "#52c41a",
                        time_label: "打款时间",
                        time: "2024.01.04 12:00:00",
                        audit_remark: ""
                    ),
                    WithdrawRecord(
                        id: 123456,
                        amount: "1000.00",
                        status: 2,
                        status_text: "已拒绝",
                        status_color: "#ff4d4f",
                        time_label: "拒绝时间",
                        time: "2024.01.04 12:00:00",
                        audit_remark: ""
                    ),
                    WithdrawRecord(
                        id: 123456,
                        amount: "1000.00",
                        status: 2,
                        status_text: "待审核",
                        status_color: "#faad14",
                        time_label: "申请时间",
                        time: "2024.01.04 12:00:00",
                        audit_remark: ""
                    ),
                    WithdrawRecord(
                        id: 123456,
                        amount: "1000.00",
                        status: 1,
                        status_text: "已打款",
                        status_color: "#52c41a",
                        time_label: "打款时间",
                        time: "2024.01.04 12:00:00",
                        audit_remark: ""
                    ),
                    WithdrawRecord(
                        id: 123456,
                        amount: "1000.00",
                        status: 2,
                        status_text: "已拒绝",
                        status_color: "#ff4d4f",
                        time_label: "拒绝时间",
                        time: "2024.01.04 12:00:00",
                        audit_remark: ""
                    )
                ]
            ),
            WithdrawMonthGroup(
                month: "2024年6月",
                total_count: 4,
                items: [
                    WithdrawRecord(
                        id: 123456,
                        amount: "1000.00",
                        status: 2,
                        status_text: "待审核",
                        status_color: "#faad14",
                        time_label: "申请时间",
                        time: "2024.01.04 12:00:00",
                        audit_remark: ""
                    ),
                    WithdrawRecord(
                        id: 123456,
                        amount: "1000.00",
                        status: 1,
                        status_text: "已打款",
                        status_color: "#52c41a",
                        time_label: "打款时间",
                        time: "2024.01.04 12:00:00",
                        audit_remark: ""
                    ),
                    WithdrawRecord(
                        id: 123456,
                        amount: "1000.00",
                        status: 2,
                        status_text: "已拒绝",
                        status_color: "#ff4d4f",
                        time_label: "拒绝时间",
                        time: "2024.01.04 12:00:00",
                        audit_remark: ""
                    ),
                    WithdrawRecord(
                        id: 123456,
                        amount: "1000.00",
                        status: 2,
                        status_text: "待审核",
                        status_color: "#faad14",
                        time_label: "申请时间",
                        time: "2024.01.04 12:00:00",
                        audit_remark: ""
                    )
                ]
            )
        ]
    }
    
    /// 跳转到指定月份
    /// - Parameter month: 目标月份
    func jumpToMonth(_ month: String) {
        currentMonth = month
        fetchWithdrawData(refresh: true)
    }
    
    // MARK: - 重写BaseViewModel方法
    
    override func refreshData() {
        fetchWithdrawData(refresh: true)
    }
    
    override func loadMoreData() {
        fetchWithdrawData(refresh: false)
    }
    
    // MARK: - Private Methods
    
    /// 处理网络错误
    override func handleError(_ error: NetworkError, operation: String = "请求") {
        // 调用父类方法处理空数据状态
        super.handleError(error, operation: operation)

        // 设置刷新状态
        refreshState = currentPage == 1 ? .refreshFailure("加载失败") : .loadMoreFailure("加载失败")

        // 添加特定的错误处理逻辑
        switch error {
        case .networkError(let response):
            print("网络错误: \(response.message)")
        case .decodingError(let message):
            print("数据解析错误: \(message)")
        case .noConnection:
            print("网络连接失败")
        case .tokenExpired:
            print("Token已过期")
        case .tokenError:
            print("Token错误")
        }
    }
}
